<template>
  <div
    class="banner-container h-[3rem] overflow-hidden flex flex-col justify-between"
    :style="{
      width: containerWidth + 'px',
    }"
    @touchstart="onTouchStart"
    @touchmove="onTouchmove"
    @touchend="onTouchend"
  >
    <div class="h-0 flex-1 flex flex-col overflow-y-auto overflow-x-hidden">
      <div
        :style="{
          width: containerWidth * list.length + 'px',
          display: 'flex',
          transition: 'transform ' + duration + 'ms ease-in-out',
        }"
        ref="rollingAreaRef"
      >
        <div
          v-for="(item, index) in list"
          :key="index"
          class="flex items-center flex-wrap"
          :style="{
            width: containerWidth + 'px',
          }"
        >
          <slot :item="item"></slot>
        </div>
      </div>
    </div>
    <div
      class="text-[.3rem] border-1 flex items-stretch"
      :style="{
        height: tabHeight,
      }"
    >
      <div
        v-for="(item, index) in list"
        class="border-1 flex justify-center items-center px-10"
        :class="indexRef === index ? 'bg-[#C7EAFF] text-[#04A5FF]' : ''"
        :key="index"
        @click="switchTo(index)"
      >
        {{ item.tab }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";

const props = defineProps<{
  list: any[];
  duration: number;
  containerWidth: number;
  tabHeight: string;
}>();

const indexRef = ref(0);
const rollingAreaRef = ref<HTMLDivElement>();
let startX = 0;
let moveX = 0;
/**
 * 切换到第几页
 * @param index
 */
const switchTo = (index: number) => {
  if (index > props.list.length - 1) {
    index = props.list.length - 1;
  }
  if (index < 0) {
    index = 0;
  }
  indexRef.value = index;
  rollingAreaRef.value!.style.transform =
    "translateX(-" + indexRef.value * props.containerWidth + "px)";
};

const onTouchStart = (e: TouchEvent) => {
  startX = e.touches[0].clientX;
};

const onTouchmove = (e: TouchEvent) => {
  moveX = e.touches[0].clientX - startX;
};

const onTouchend = () => {
  if (moveX > 50) {
    switchTo(indexRef.value - 1);
  } else if (moveX < -50) {
    switchTo(indexRef.value + 1);
  } else {
    switchTo(indexRef.value);
  }
};

defineExpose({
  switchTo,
});
</script>

<style scoped lang="less">
.banner-container {
  width: 100%;
  height: 100%;
}
</style>
