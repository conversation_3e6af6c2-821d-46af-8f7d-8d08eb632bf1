<template>
  <slot></slot>
  <Teleport to="body" v-if="atState">
    <div
      class="fixed bg-white border border-gray-300 shadow-lg mb-2 rounded-6 w-200 text-16"
      :style="{ left: atState.x + 'px', bottom: atState.y + 'px' }"
    >
      <div class="p-8">群成员</div>
      <div
        class="overflow-y-auto whitespace-nowrap"
        :class="{ 'max-h-400': terminal, 'max-h-600': !terminal }"
        ref="selectorEle"
      >
        <div
          class="flex items-center justify-between py-4 px-8"
          :class="{ 'text-red bg-gray-300': i === atState.selectIndex }"
          @mousedown.stop.prevent="selectMember(i)"
          v-for="(member, i) in filteredGroupMembers"
        >
          <img
            class="w-30 h-30 object-cover"
            :src="member.avatar"
            v-if="i"
          /><span v-else></span>
          <span>{{ member.nickName }}</span>
        </div>
      </div>
    </div>
  </Teleport>
</template>

<script lang="ts" setup>
import { GroupMember, AtUser } from "@/api/chat";
import { scrollIntoView } from "@/utils/dom";
import { assert } from "@/utils/tools";
import { ref, computed, onMounted, onUnmounted, nextTick, inject } from "vue";
import { useSessionStore } from "@/store/index";
import { storeToRefs } from "pinia";

const { inputRef } = defineProps<{
  inputRef: HTMLInputElement | HTMLDivElement | HTMLTextAreaElement;
}>();
const flyMsg = defineModel<string>();
defineExpose({
  getMsgContent() {
    const atInfo = {
      atUsers: [] as AtUser[],
      isAtAll: false,
      atAllFlag: false,
      text: "",
    };
    if (isRichText(inputRef)) {
      const doc = new DOMParser().parseFromString(
        inputRef.innerHTML,
        "text/html",
      );
      const textSegments: string[] = [];
      for (const node of doc.body.childNodes) {
        if (node.nodeType === Node.ELEMENT_NODE) {
          const ele = node as HTMLElement;
          if (ele.tagName.toLowerCase() === "span") {
            const userName = ele.getAttribute("data-user-name")!;
            if (userName === "所有人") {
              atInfo.isAtAll = true;
              atInfo.atUsers = [];
            } else {
              const member: GroupMember = JSON.parse(
                ele.getAttribute("data-user-info")!,
              );
              atInfo.atUsers.push({
                faceUrl: member.avatar,
                userId: member.mucangId,
                nickName: member.nickName,
              });
            }
            textSegments.push(`@${userName} `);
          } else if (ele.tagName.toLowerCase() === "br") {
            textSegments.push("\n");
          }
        } else if (node.nodeType === Node.TEXT_NODE) {
          textSegments.push(node.nodeValue || "");
        }
      }
      atInfo.text = textSegments.join("");
    } else {
      atInfo.text = inputRef.value;
      const matches = inputRef.value.matchAll(/@([^\s]+)\s/g);
      for (const match of matches) {
        const userName = match[1];
        const member = groupMembers.value.find((m) => m.nickName === userName);
        if (member) {
          if (member.nickName === "所有人") {
            atInfo.isAtAll = true;
            atInfo.atUsers = [];
            break;
          }
          atInfo.atUsers.push({
            faceUrl: member.avatar,
            userId: member.mucangId,
            nickName: member.nickName,
          });
        }
      }
    }

    atInfo.atAllFlag = atInfo.isAtAll;
    return atInfo;
  },
  insertEmoji(text: string, imgUrl: string) {
    onPickEmoji(text, imgUrl);
  },
});
const isRichText = (
  inputRef: HTMLInputElement | HTMLDivElement | HTMLTextAreaElement,
): inputRef is HTMLDivElement => inputRef instanceof HTMLDivElement;

const { session } = storeToRefs(useSessionStore());
const terminal = inject<number>("terminal");

const groupMembers = computed(() => {
  return [
    {
      mucangId: "",
      nickName: "所有人",
      avatar: "",
    },
    ...(session.value!.groupMembers || []),
  ];
});

const filteredGroupMembers = computed(() => {
  const text = atState.value?.text;
  if (!text) {
    return groupMembers.value;
  }

  return groupMembers.value.filter(
    (m, i) => i === 0 || m.nickName.includes(text),
  );
});
const atState = ref<{
  text: string;
  selectIndex: number;
  x: number;
  y: number;
} | null>(null);
const selectorEle = ref<HTMLDivElement>();

const handleSelectionChange = () => {
  if (!session.value!.isGroup) {
    return;
  }

  console.log("光标改变");
  atState.value = null;

  if (isRichText(inputRef)) {
    const selection = window.getSelection();
    if (!selection || selection.rangeCount !== 1) {
      return;
    }

    const range = selection.getRangeAt(0);
    if (
      !inputRef.contains(range.commonAncestorContainer) ||
      !range.collapsed ||
      range.startContainer.nodeType !== Node.TEXT_NODE
    ) {
      return;
    }

    const startPos = range.startOffset;
    const value = range.startContainer.nodeValue!;
    for (let i = startPos - 1; i >= 0; i--) {
      if (value[i].trim() !== value[i]) {
        console.log("检测到空格");
        return;
      } else if (value[i] === "@") {
        const br = range.getBoundingClientRect();
        atState.value = {
          text: value.substring(i + 1, startPos),
          selectIndex: 0,
          x: br.left,
          y: document.documentElement.clientHeight - br.top,
        };
        break;
      }
    }
  } else {
    if (
      document.activeElement !== inputRef ||
      inputRef.selectionStart !== inputRef.selectionEnd
    ) {
      return;
    }

    const startPos = inputRef.selectionStart;
    const value = inputRef.value;
    for (let i = startPos - 1; i >= 0; i--) {
      if (value[i].trim() !== value[i]) {
        console.log("检测到空格");
        return;
      } else if (value[i] === "@") {
        const br = inputRef.getBoundingClientRect();
        atState.value = {
          text: value.substring(i + 1, startPos),
          selectIndex: 0,
          x: br.left,
          y: document.documentElement.clientHeight - br.top,
        };
        break;
      }
    }
  }
};

const handleKeyShortCut = (event: KeyboardEvent) => {
  if (!session.value!.isGroup) {
    return;
  }

  console.log("按下按键", event.key);

  if (atState.value) {
    // 检查按下的键是否是向上或向下箭头
    if (event.key === "ArrowUp" || event.key === "ArrowDown") {
      event.preventDefault(); // 阻止默认行为
      event.stopImmediatePropagation();
      event.stopPropagation();
      let currentIndex = atState.value.selectIndex;
      // 根据按下的键更新索引
      if (event.key === "ArrowUp") {
        currentIndex =
          currentIndex > 0
            ? currentIndex - 1
            : filteredGroupMembers.value.length - 1;
      } else if (event.key === "ArrowDown") {
        currentIndex =
          currentIndex < filteredGroupMembers.value.length - 1
            ? currentIndex + 1
            : 0;
      }
      atState.value.selectIndex = currentIndex;
      nextTick(() => {
        scrollIntoView(
          selectorEle.value!.children[currentIndex] as HTMLElement,
        );
      });
    } else if (event.key === "Enter") {
      // 检查按下的键是否是回车键
      event.preventDefault(); // 阻止默认行为，例如提交表单
      event.stopImmediatePropagation();
      event.stopPropagation();
      selectMember(atState.value.selectIndex);
    }
  }
};

const onPickUser = (user: GroupMember) => {
  const userName = user.nickName;

  if (isRichText(inputRef)) {
    const selection = window.getSelection()!;
    if (selection.rangeCount === 0) return; // 如果没有选择范围，则不执行任何操作

    const range = selection.getRangeAt(0);

    assert(range.collapsed && range.startContainer.nodeType === Node.TEXT_NODE);
    const startPos = range.startOffset;
    const value = range.startContainer.nodeValue!;
    const beforeText = value.substring(0, startPos);
    const atPos = beforeText.lastIndexOf("@");

    if (atPos === -1) {
      return;
    }

    range.setStart(range.startContainer, atPos);
    range.deleteContents(); // 删除当前选中的内容（如果有的话）
    assert(range.collapsed && range.startContainer.nodeType === Node.TEXT_NODE);
    // 创建一个文本节点
    const textNode = document.createElement("span");
    textNode.innerHTML = `@${userName}&nbsp;`;
    textNode.className = "text-blue-500";
    textNode.setAttribute("contenteditable", "false");
    textNode.setAttribute("data-user-name", userName);
    textNode.setAttribute("data-user-info", JSON.stringify(user));

    // 将文本节点插入到当前光标位置
    range.insertNode(textNode);
    flyMsg.value = inputRef.innerHTML;

    nextTick(() => {
      // 清除当前的选择，并将光标移动到新插入的文本之后
      range.setStartAfter(textNode);
      range.setEndAfter(textNode);
      selection.removeAllRanges();
      selection.addRange(range);
    });
  } else {
    // 纯input
    // 从当前鼠标位置往前找，找到@符号，将这一段替换
    const inputEle = inputRef;
    const startPos = inputEle.selectionStart!;
    const value = inputEle.value;
    const beforeText = value.substring(0, startPos);

    const atPos = beforeText.lastIndexOf("@");
    if (atPos === -1) {
      return;
    }
    const newInput =
      value.slice(0, atPos) + `@${userName} ` + value.slice(startPos);
    flyMsg.value = newInput;
    nextTick(() => {
      inputEle.selectionStart = inputEle.selectionEnd =
        atPos + userName.length + 2;
    });
  }
};

const onPickEmoji = (text: string, _imgUrl: string) => {
  if (isRichText(inputRef)) {
  } else {
    // 纯input
    // 插入文本
    const inputEle = inputRef;
    const startPos = inputEle.selectionStart!;
    const value = inputEle.value;
    const newInput = value.slice(0, startPos) + text + value.slice(startPos);
    flyMsg.value = newInput;
    nextTick(() => {
      inputEle.selectionStart = inputEle.selectionEnd = startPos + text.length;
      terminal === 1 ? "" : inputEle.focus();
    });
  }
};

const selectMember = (index: number) => {
  onPickUser(filteredGroupMembers.value[index]);
  atState.value = null;
};

onMounted(() => {
  nextTick(() => {
    (inputRef as HTMLInputElement).addEventListener(
      "input",
      handleSelectionChange,
    );
    (inputRef as HTMLInputElement).addEventListener(
      "keydown",
      handleKeyShortCut,
      true,
    );
  });
  document.addEventListener("selectionchange", handleSelectionChange);
});

onUnmounted(() => {
  document.removeEventListener("selectionchange", handleSelectionChange);
});
</script>
