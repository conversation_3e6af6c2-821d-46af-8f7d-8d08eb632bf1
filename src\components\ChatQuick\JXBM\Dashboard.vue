<template>
  <div
    class="mt-17 mb-12 font-semibold text-[rgba(51,51,51,0.85)]"
    :class="{
      'text-15': !terminal,
      'text-[.32rem]': terminal,
    }"
  >
    基础信息
  </div>
  <div
    class="border-1 border-solid border-[#e6eef4]"
    :class="{
      'text-15/39': !terminal,
      'text-[.32rem]/39': terminal,
    }"
  >
    <div class="flex">
      <div
        class="pl-15 w-100 text-left bg-[#f6f9fb] rounded-tl-5 border-1 border-solid border-[#e6eef4]"
      >
        木仓昵称
      </div>
      <div
        class="w-0 flex-1 pl-14 bg-white rounded-tr-5 border-1 border-solid border-[#e6eef4] [border-left:0]"
      >
        {{ dashboard?.nickName }}
      </div>
    </div>
    <div class="flex">
      <div
        class="pl-15 w-100 text-left bg-[#f6f9fb] border-1 border-solid border-[#e6eef4] [border-top:0]"
      >
        木仓ID
      </div>
      <div
        class="w-0 flex-1 pl-14 bg-white flex border-1 border-solid border-[#e6eef4] [border-top:0] [border-left:0]"
      >
        <div class="flex-1 whitespace-nowrap overflow-hidden text-ellipsis">
          {{ dashboard?.mucangId }}
        </div>
        <div
          class="w-60 ml-10 cursor-pointer"
          style="color: #04a5ff"
          @click="copyMucangId(dashboard?.mucangId)"
          v-if="dashboard?.mucangId"
        >
          复制
        </div>
      </div>
    </div>
    <div class="flex">
      <div
        class="pl-15 w-100 text-left bg-[#f6f9fb] border-1 border-solid border-[#e6eef4] [border-top:0]"
      >
        车型
      </div>
      <div
        class="w-0 flex-1 pl-14 bg-white border-1 border-solid border-[#e6eef4] [border-left:0] [border-top:0]"
      >
        {{ dashboard?.carType && CarTypeMapper[dashboard.carType] }}
      </div>
    </div>
    <div class="flex">
      <div
        class="pl-15 w-100 text-left bg-[#f6f9fb] border-1 border-solid border-[#e6eef4] [border-top:0]"
      >
        城市
      </div>
      <div
        class="w-0 flex-1 pl-14 bg-white border-1 border-solid border-[#e6eef4] [border-left:0] [border-top:0]"
      >
        {{ dashboard?.cityName }}
      </div>
    </div>
    <div class="flex">
      <div
        class="pl-15 w-100 text-left bg-[#f6f9fb] border-1 border-solid border-[#e6eef4] [border-top:0]"
      >
        手机号
      </div>
      <div
        class="w-0 flex-1 pl-14 bg-white border-1 border-solid border-[#e6eef4] [border-left:0] [border-top:0]"
      >
        <div>{{ dashboard?.phoneMask }}</div>
        <span
          class="w-60 cursor-pointer"
          style="color: #04a5ff"
          @click="showUserPhone"
          v-if="dashboard?.phoneMask"
          >显示</span
        >
        <span
          class="w-60 ml-10 cursor-pointer"
          style="color: #04a5ff"
          @click="onCallClick"
          v-if="dashboard?.phoneMask"
          >呼叫</span
        >
      </div>
    </div>
    <div class="flex">
      <div
        class="pl-15 w-100 text-left bg-[#f6f9fb] border-1 border-solid border-[#e6eef4] [border-top:0]"
      >
        姓名
      </div>
      <div
        class="w-0 flex-1 pl-14 bg-white flex border-1 border-solid border-[#e6eef4] [border-left:0] [border-top:0]"
      >
        <div class="flex-1">{{ dashboard?.name || "--" }}</div>
        <div
          class="w-60 ml-10 cursor-pointer"
          style="color: #04a5ff"
          @click="editField('name', '姓名')"
        >
          编辑
        </div>
      </div>
    </div>
    <div class="flex">
      <div
        class="pl-15 w-100 text-left bg-[#f6f9fb] border-1 border-solid border-[#e6eef4] [border-top:0]"
      >
        职业
      </div>
      <div
        class="w-0 flex-1 pl-14 bg-white flex border-1 border-solid border-[#e6eef4] [border-left:0] [border-top:0]"
      >
        <div class="flex-1">{{ dashboard?.work || "--" }}</div>
        <div
          class="w-60 ml-10 cursor-pointer"
          style="color: #04a5ff"
          @click="editField('work', '职业')"
        >
          编辑
        </div>
      </div>
    </div>
    <div class="flex">
      <div
        class="pl-15 w-100 text-left bg-[#f6f9fb] border-1 border-solid border-[#e6eef4] [border-top:0]"
      >
        年龄
      </div>
      <div
        class="w-0 flex-1 pl-14 bg-white flex border-1 border-solid border-[#e6eef4] [border-left:0] [border-top:0]"
      >
        <div class="flex-1">{{ dashboard?.age || "--" }}</div>
        <div
          class="w-60 ml-10 cursor-pointer"
          style="color: #04a5ff"
          @click="editField('age', '年龄')"
        >
          编辑
        </div>
      </div>
    </div>
    <div class="flex">
      <div
        class="pl-15 w-100 text-left bg-[#f6f9fb] border-1 border-solid border-[#e6eef4] [border-top:0]"
      >
        性别
      </div>
      <div
        class="w-0 flex-1 pl-14 bg-white flex border-1 border-solid border-[#e6eef4] [border-left:0] [border-top:0]"
      >
        <div class="flex-1">
          {{
            dashboard?.gender !== undefined
              ? GenderMapper[dashboard.gender]
              : "--"
          }}
        </div>
        <div
          class="w-60 ml-10 cursor-pointer"
          style="color: #04a5ff"
          @click="editGender"
        >
          编辑
        </div>
      </div>
    </div>
    <div class="flex">
      <div
        class="pl-15 w-100 text-left bg-[#f6f9fb] border-1 border-solid border-[#e6eef4] [border-top:0]"
      >
        VIP类型
      </div>
      <div
        class="w-0 flex-1 pl-14 bg-white border-1 border-solid border-[#e6eef4] [border-left:0] [border-top:0]"
      >
        {{ dashboard?.vipTypes }}
      </div>
    </div>
    <div class="flex">
      <div
        class="pl-15 w-100 text-left bg-[#f6f9fb] border-1 border-solid border-[#e6eef4] [border-top:0]"
      >
        做题数
      </div>
      <div
        class="w-0 flex-1 pl-14 bg-white border-1 border-solid border-[#e6eef4] [border-left:0] [border-top:0]"
      >
        {{ dashboard?.totalExerciseCount || "--" }}
      </div>
    </div>
    <div class="flex">
      <div
        class="pl-15 w-100 text-left bg-[#f6f9fb] border-1 border-solid border-[#e6eef4] [border-top:0]"
      >
        模考次数
      </div>
      <div
        class="w-0 flex-1 pl-14 bg-white border-1 border-solid border-[#e6eef4] [border-left:0] [border-top:0]"
      >
        {{ dashboard?.mockTimes || "--" }}
      </div>
    </div>
    <div class="flex">
      <div
        class="pl-15 w-100 text-left bg-[#f6f9fb] border-1 border-solid border-[#e6eef4] [border-top:0]"
      >
        近五次模考平均分
      </div>
      <div
        class="w-0 flex-1 pl-14 bg-white border-1 border-solid border-[#e6eef4] [border-left:0] [border-top:0]"
      >
        {{ dashboard?.latestFiveTimeAvgScore || "--" }}
      </div>
    </div>
    <div class="flex">
      <div
        class="pl-15 w-100 text-left bg-[#f6f9fb] border-1 border-solid border-[#e6eef4] [border-top:0]"
      >
        跟进人
      </div>
      <div
        class="w-0 flex-1 pl-14 bg-white border-1 border-solid border-[#e6eef4] [border-left:0] [border-top:0]"
      >
        <div v-if="dashboard?.followUserName">
          {{ dashboard?.followUserName }}
        </div>
        <div v-else>
          <span>暂无</span
          ><span
            class="text-[#04a5ff] ml-10 cursor-pointer"
            @click="onFollowUserClick"
            >我来跟进</span
          >
        </div>
      </div>
    </div>
    <div class="flex">
      <div
        class="pl-15 w-100 text-left bg-[#f6f9fb] border-1 border-solid border-[#e6eef4] [border-top:0]"
      >
        客户备注
      </div>
      <div
        class="w-0 flex-1 pl-14 bg-white border-1 border-solid border-[#e6eef4] [border-left:0] [border-top:0]"
      >
        <CustomerRemarks v-if="dashboard?.userNo" :user-no="dashboard.userNo" />
      </div>
    </div>
    <div class="flex">
      <div
        class="pl-15 w-100 text-left bg-[#f6f9fb] border-1 border-solid border-[#e6eef4] [border-top:0]"
      >
        客户标签
      </div>
      <div
        class="w-0 flex-1 pl-14 bg-white border-1 border-solid border-[#e6eef4] [border-left:0] [border-top:0]"
      >
        <CustomerTags v-if="dashboard?.userNo" :user-no="dashboard.userNo" />
      </div>
    </div>
    <div class="flex">
      <div
        class="pl-15 w-100 text-left bg-[#f6f9fb] border-1 border-solid border-[#e6eef4] [border-top:0] rounded-bl-5"
      >
        学车地图
      </div>
      <div
        class="w-0 flex-1 pl-14 bg-white border-1 border-solid border-[#e6eef4] [border-left:0] [border-top:0] rounded-br-5"
      >
        <span
          class="text-[#04a5ff] cursor-pointer"
          @click="
            aMapPopUpCompRef?.open(
              session!.sessionKey,
              dashboard?.latitude,
              dashboard?.longitude,
              dashboard?.mucangId,
            )
          "
          >查看</span
        >
      </div>
    </div>
  </div>
  <AMapPopUp ref="aMapPopUpCompRef"></AMapPopUp>
  <CallModel ref="callModelCompRef"></CallModel>
</template>

<script lang="ts" setup>
import { ref, inject } from "vue";

import {
  MonaDashboard,
  queryUserPhone,
  salesFollowRecordBind,
  queryDashboard,
  updateUserInfo,
} from "@/api/jxbm";
import { Session } from "@/api/chat";
import { makeToast, prompt } from "@/utils/dom";
import { CarTypeMapper } from "@/utils/helpers";
import CustomerRemarks from "./CustomerRemarks.vue";
import CustomerTags from "./CustomerTags.vue";
import AMapPopUp from "@/components/Common/AMapPopUp/index.vue";
import CallModel from "@/components/ChatQuick/Common/CallModel.vue";

const props = defineProps<{
  session?: Session;
}>();
const emit = defineEmits<{
  (e: "update:modelValue", dashboard: MonaDashboard): void;
}>();

const dashboard = ref<MonaDashboard>();
const aMapPopUpCompRef = ref<InstanceType<typeof AMapPopUp>>();
const callModelCompRef = ref<InstanceType<typeof CallModel>>();
const terminal = inject("terminal") as 0 | 1;

const GenderMapper: Record<number, string> = {
  0: "女",
  1: "男",
  99: "未知",
};

const copyMucangId = (mucangId: string) => {
  try {
    navigator.clipboard.writeText(mucangId);
    makeToast("复制成功");
  } catch {
    makeToast("复制失败");
  }
};

const showUserPhone = async () => {
  const { value: phone } = await queryUserPhone({
    mucangId: dashboard.value!.mucangId,
  });
  prompt({
    title: "用户手机号",
    content: `<div style="font-size: 20px;">${phone}</div>`,
    terminal,
    showCancel: false,
  });
};

/**
 * 销售人跟进
 */
const onFollowUserClick = async () => {
  if (dashboard.value?.mucangId) {
    const { value } = await salesFollowRecordBind({
      userId: dashboard.value?.mucangId,
    });
    if (value) {
      makeToast("绑定成功");
    } else {
      makeToast("绑定失败");
    }
  }
};

/**
 * 呼叫用户
 */
const onCallClick = async () => {
  if (dashboard.value) {
    // 调用呼叫组件，传递完整的参数
    callModelCompRef.value?.open(
      props.session!.sessionKey, // 会话密钥
      dashboard.value.mucangId, // 用户ID
      dashboard.value.cityCode || "", // 城市编码，如果没有则使用空字符串
    );
  }
};

/**
 * 编辑字段
 */
const editField = async (field: "name" | "work" | "age", fieldName: string) => {
  if (!props.session?.sessionKey) return;

  const currentValue = dashboard.value?.[field] || "";
  const inputType = field === "age" ? "number" : "text";

  try {
    const result = await prompt({
      title: `编辑${fieldName}`,
      content: `<input type="${inputType}" id="editInput" value="${currentValue}" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;" placeholder="请输入${fieldName}">`,
      terminal,
      showCancel: true,
    });

    if (result) {
      const input = document.getElementById("editInput") as HTMLInputElement;
      const newValue = input?.value?.trim();

      if (newValue && newValue !== String(currentValue)) {
        try {
          const updateData: any = { sessionKey: props.session.sessionKey };
          if (field === "age") {
            updateData[field] = parseInt(newValue);
          } else {
            updateData[field] = newValue;
          }

          const { value } = await updateUserInfo(updateData);
          if (value) {
            if (dashboard.value) {
              (dashboard.value as any)[field] =
                field === "age" ? parseInt(newValue) : newValue;
              emit("update:modelValue", dashboard.value);
            }
            makeToast(`${fieldName}更新成功`);
          } else {
            makeToast(`${fieldName}更新失败`);
          }
        } catch (error) {
          makeToast(`${fieldName}更新失败`);
        }
      }
    }
  } catch (error) {
    // 用户取消操作
  }
};

/**
 * 编辑性别
 */
const editGender = async () => {
  if (!props.session?.sessionKey) return;

  const currentGender = dashboard.value?.gender ?? 99;
  const genderOptions = Object.entries(GenderMapper).map(([value, label]) => ({
    value: Number(value),
    label,
  }));

  const optionsHtml = genderOptions
    .map(
      (option) =>
        `<label style="display: block; margin: 8px 0;">
      <input type="radio" name="gender" value="${option.value}" ${currentGender === option.value ? "checked" : ""}> ${option.label}
    </label>`,
    )
    .join("");

  try {
    const result = await prompt({
      title: "编辑性别",
      content: `<div>${optionsHtml}</div>`,
      terminal,
      showCancel: true,
    });

    if (result) {
      const selectedRadio = document.querySelector(
        'input[name="gender"]:checked',
      ) as HTMLInputElement;
      const newGender = selectedRadio ? parseInt(selectedRadio.value) : 99;

      if (newGender !== currentGender) {
        try {
          const { value } = await updateUserInfo({
            sessionKey: props.session.sessionKey,
            gender: newGender,
          });
          if (value) {
            if (dashboard.value) {
              dashboard.value.gender = newGender;
              emit("update:modelValue", dashboard.value);
            }
            makeToast("性别更新成功");
          } else {
            makeToast("性别更新失败");
          }
        } catch (error) {
          makeToast("性别更新失败");
        }
      }
    }
  } catch (error) {
    // 用户取消操作
  }
};

const fetchDashBoardInfo = async () => {
  dashboard.value = await queryDashboard({
    sessionKey: props.session!.sessionKey,
  });
  // 需要同步v-model
  emit("update:modelValue", dashboard.value);
};

defineExpose({
  fetchDashBoardInfo,
});
</script>
