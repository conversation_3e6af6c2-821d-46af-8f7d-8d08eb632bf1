<template>
  <div
    class="mt-17 mb-12 font-semibold text-[rgba(51,51,51,0.85)]"
    :class="{
      'text-15': !terminal,
      'text-[.32rem]': terminal,
    }"
  >
    基础信息
  </div>
  <div
    class="border-1 border-solid border-[#e6eef4]"
    :class="{
      'text-15/39': !terminal,
      'text-[.32rem]/39': terminal,
    }"
  >
    <div class="flex">
      <div
        class="pl-15 w-100 text-left bg-[#f6f9fb] rounded-tl-5 border-1 border-solid border-[#e6eef4]"
      >
        木仓昵称
      </div>
      <div
        class="w-0 flex-1 pl-14 bg-white rounded-tr-5 border-1 border-solid border-[#e6eef4] [border-left:0]"
      >
        {{ dashboard?.nickName }}
      </div>
    </div>
    <div class="flex">
      <div
        class="pl-15 w-100 text-left bg-[#f6f9fb] border-1 border-solid border-[#e6eef4] [border-top:0]"
      >
        木仓ID
      </div>
      <div
        class="w-0 flex-1 pl-14 bg-white flex border-1 border-solid border-[#e6eef4] [border-top:0] [border-left:0]"
      >
        <div class="flex-1 whitespace-nowrap overflow-hidden text-ellipsis">
          {{ dashboard?.mucangId }}
        </div>
        <div
          class="w-60 ml-10 cursor-pointer"
          style="color: #04a5ff"
          @click="copyMucangId(dashboard?.mucangId)"
          v-if="dashboard?.mucangId"
        >
          复制
        </div>
      </div>
    </div>
    <div class="flex">
      <div
        class="pl-15 w-100 text-left bg-[#f6f9fb] border-1 border-solid border-[#e6eef4] [border-top:0]"
      >
        车型
      </div>
      <div
        class="w-0 flex-1 pl-14 bg-white border-1 border-solid border-[#e6eef4] [border-left:0] [border-top:0]"
      >
        {{ dashboard?.carType && CarTypeMapper[dashboard.carType] }}
      </div>
    </div>
    <div class="flex">
      <div
        class="pl-15 w-100 text-left bg-[#f6f9fb] border-1 border-solid border-[#e6eef4] [border-top:0]"
      >
        城市
      </div>
      <div
        class="w-0 flex-1 pl-14 bg-white border-1 border-solid border-[#e6eef4] [border-left:0] [border-top:0]"
      >
        {{ dashboard?.cityName }}
      </div>
    </div>
    <div class="flex">
      <div
        class="pl-15 w-100 text-left bg-[#f6f9fb] border-1 border-solid border-[#e6eef4] [border-top:0] rounded-bl-5"
      >
        手机号
      </div>
      <div
        class="w-0 flex-1 pl-14 bg-white border-1 border-solid border-[#e6eef4] [border-left:0] [border-top:0] rounded-br-5"
      >
        <div>{{ dashboard?.phoneMask }}</div>
        <span
          class="w-60 cursor-pointer"
          style="color: #04a5ff"
          @click="showUserPhone"
          v-if="dashboard?.phoneMask"
          >显示</span
        >
        <span
          class="w-60 ml-10 cursor-pointer"
          style="color: #04a5ff"
          @click="onCallClick"
          v-if="dashboard?.phoneMask"
          >呼叫</span
        >
      </div>
    </div>
    <div class="flex">
      <div
        class="pl-15 w-100 text-left bg-[#f6f9fb] border-1 border-solid border-[#e6eef4] [border-top:0]"
      >
        跟进人
      </div>
      <div
        class="w-0 flex-1 pl-14 bg-white border-1 border-solid border-[#e6eef4] [border-left:0] [border-top:0]"
      >
        <div v-if="dashboard?.followUserName">
          {{ dashboard?.followUserName }}
        </div>
        <div v-else>
          <span>暂无</span
          ><span
            class="text-[#04a5ff] ml-10 cursor-pointer"
            @click="onFollowUserClick"
            >我来跟进</span
          >
        </div>
      </div>
    </div>
    <div class="flex">
      <div
        class="pl-15 w-100 text-left bg-[#f6f9fb] border-1 border-solid border-[#e6eef4] [border-top:0]"
      >
        客户备注
      </div>
      <div
        class="w-0 flex-1 pl-14 bg-white border-1 border-solid border-[#e6eef4] [border-left:0] [border-top:0]"
      >
        <CustomerRemarks v-if="dashboard?.userNo" :user-no="dashboard.userNo" />
      </div>
    </div>
    <div class="flex">
      <div
        class="pl-15 w-100 text-left bg-[#f6f9fb] border-1 border-solid border-[#e6eef4] [border-top:0] rounded-bl-5"
      >
        客户标签
      </div>
      <div
        class="w-0 flex-1 pl-14 bg-white border-1 border-solid border-[#e6eef4] [border-left:0] [border-top:0] rounded-br-5"
      >
        <CustomerTags v-if="dashboard?.userNo" :user-no="dashboard.userNo" />
      </div>
    </div>
    <div class="flex">
      <div
        class="pl-15 w-100 text-left bg-[#f6f9fb] border-1 border-solid border-[#e6eef4] [border-top:0] rounded-bl-5"
      >
        学车地图
      </div>
      <div
        class="w-0 flex-1 pl-14 bg-white border-1 border-solid border-[#e6eef4] [border-left:0] [border-top:0]"
      >
        <span
          class="text-[#04a5ff] cursor-pointer"
          @click="
            aMapPopUpCompRef?.open(
              session!.sessionKey,
              dashboard?.latitude,
              dashboard?.longitude,
              dashboard?.mucangId,
            )
          "
          >查看</span
        >
      </div>
    </div>
  </div>
  <AMapPopUp ref="aMapPopUpCompRef"></AMapPopUp>
  <CallModel ref="callModelCompRef"></CallModel>
</template>

<script lang="ts" setup>
import { ref, inject } from "vue";

import {
  MonaDashboard,
  queryUserPhone,
  salesFollowRecordBind,
  queryDashboard,
} from "@/api/jxbm";
import { Session } from "@/api/chat";
import { makeToast, prompt } from "@/utils/dom";
import { CarTypeMapper } from "@/utils/helpers";
import CustomerRemarks from "./CustomerRemarks.vue";
import CustomerTags from "./CustomerTags.vue";
import AMapPopUp from "@/components/Common/AMapPopUp/index.vue";
import CallModel from "@/components/ChatQuick/Common/CallModel.vue";

const props = defineProps<{
  session?: Session;
}>();
const emit = defineEmits<{
  (e: "update:modelValue", dashboard: MonaDashboard): void;
}>();

const dashboard = ref<MonaDashboard>();
const aMapPopUpCompRef = ref<InstanceType<typeof AMapPopUp>>();
const callModelCompRef = ref<InstanceType<typeof CallModel>>();
const terminal = inject("terminal") as 0 | 1;

const copyMucangId = (mucangId: string) => {
  try {
    navigator.clipboard.writeText(mucangId);
    makeToast("复制成功");
  } catch {
    makeToast("复制失败");
  }
};

const showUserPhone = async () => {
  const { value: phone } = await queryUserPhone({
    mucangId: dashboard.value!.mucangId,
  });
  prompt({
    title: "用户手机号",
    content: `<div style="font-size: 20px;">${phone}</div>`,
    terminal,
    showCancel: false,
  });
};

/**
 * 销售人跟进
 */
const onFollowUserClick = async () => {
  if (dashboard.value?.mucangId) {
    const { value } = await salesFollowRecordBind({
      userId: dashboard.value?.mucangId,
    });
    if (value) {
      makeToast("绑定成功");
    } else {
      makeToast("绑定失败");
    }
  }
};

/**
 * 呼叫用户
 */
const onCallClick = async () => {
  if (dashboard.value) {
    // 调用呼叫组件，传递完整的参数
    callModelCompRef.value?.open(
      props.session!.sessionKey, // 会话密钥
      dashboard.value.mucangId, // 用户ID
      dashboard.value.cityCode || "", // 城市编码，如果没有则使用空字符串
    );
  }
};

const fetchDashBoardInfo = async () => {
  dashboard.value = await queryDashboard({
    sessionKey: props.session!.sessionKey,
  });
  // 需要同步v-model
  emit("update:modelValue", dashboard.value);
};

defineExpose({
  fetchDashBoardInfo,
});
</script>
