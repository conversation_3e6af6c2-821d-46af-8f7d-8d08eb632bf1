<!-- 聊天分组 -->
<template>
  <div class="px-4" v-for="(item, index) in sessionTab" :key="item.tagCode">
    <div
      class="px-10 flex justify-between items-center h-35 cursor-pointer"
      :class="{
        'bg-[#e6f2fb]': item.tagCode === selectedTab,
        'h-35': !terminal,
        'h-[.5rem]': terminal,
      }"
      @click="onSwitchTab(item.tagCode, index)"
    >
      <div
        class="label"
        :class="{
          'h-19 text-13/18': !terminal,
          'h-[.5rem] text-[.3rem]/[.5rem]': terminal,
        }"
      >
        <span
          :class="{
            'text-[#04a5ff]': item.tagCode === selectedTab,
          }"
          >{{ item.tagName }}</span
        >
      </div>
      <div v-if="!item.unreadCount"></div>
      <div
        v-else
        class="messageNum bg-[#ff4a40]"
        :class="{
          'h-15 text-11/15 w-36 rounded-9': !terminal,
          'h-[.3rem] rounded-[.3rem] w-[.5rem] text-[.25rem]/[.3rem]': terminal,
        }"
      >
        <span v-if="item.unreadCount > 999"> 999+ </span>
        <span v-else>
          {{ item.unreadCount }}
        </span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Tabs } from "@/api/chat";
import { inject } from "vue";
defineProps<{
  sessionTab: Set<Tabs> | undefined;
  selectedTab: string;
}>();
const emit = defineEmits<{
  (e: "switch", code: string, index: number): void;
}>();
const onSwitchTab = (code: string, index: number) => {
  emit("switch", code, index);
};
const terminal = inject("terminal");
</script>

<style scoped lang="less">
.label {
  // height: 19px;
  // font-size: 13px;
  font-family:
    PingFang SC,
    PingFang SC-Regular;
  text-align: left;
  color: #333333;
  // line-height: 18px;
}
.messageNum {
  font-family:
    PingFang SC,
    PingFang SC-Semibold;
  text-align: center;
  color: #ffffff;
}
</style>
