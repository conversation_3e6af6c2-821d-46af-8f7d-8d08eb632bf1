<template>
  <div class="flex flex-col">
    <div class="search flex" :class="terminal ? 'h-[.7rem]' : 'h-36'">
      <div
        class="flex items-center rounded-5 border-1 border-solid border-[#cccfd0] bg-[#F8FCFF] flex-1 mr-9"
      >
        <img
          src="@/assets/top_search.png"
          alt=""
          class="ml-14"
          :class="{
            'w-14 h-14': !terminal,
            'w-[.3rem] h-[.3rem]': terminal,
          }"
        />
        <input
          type="text"
          class="outline-none border-0 w-full h-full mx-9 bg-transparent"
          :class="{
            'text-14/20': !terminal,
            'text-[.3rem]/[.6rem]': terminal,
          }"
          ref="inputDomRef"
          placeholder="输入话术标题"
          v-model="searchInputRef"
        />
      </div>
      <button
        class="bg-[#ff822d] text-[#ffffff]"
        :class="{
          'w-59 rounded-8 text-13': !terminal,
          'w-[1.5rem] rounded-8 text-[.3rem]/[.7rem]': terminal,
        }"
        @click="onSearchClick"
      >
        搜索
      </button>
    </div>
    <div class="mt-17" v-if="searchInstructionDataRef">
      <div
        class="text-[#464646] text-12 font-bold"
        v-if="searchInstructionDataRef"
      >
        搜索结果
      </div>
      <ScriptList
        :bgFn="bgFn"
        :disable="disable"
        :onPreviewFn="onPreviewFn"
        :dataList="searchInstructionDataRef!"
        v-if="searchInstructionDataRef && searchInstructionDataRef?.length > 0"
        @send="onSendClick"
      >
        <template #title="{ item }">
          <slot name="title" :item="item"></slot>
        </template>
        <template #default="{ item }">
          <slot :item="item"></slot>
        </template>
      </ScriptList>
      <div
        v-else-if="
          searchInstructionDataRef && searchInstructionDataRef.length === 0
        "
        class="py-44 text-center text-13 text-[#333333]"
      >
        暂无搜索结果
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { inject, ref } from "vue";
import ScriptList from "../ScriptList.vue";
import { ScriptListType } from "./type";
const props = defineProps<{
  headleSearch: (inputValue: string) => any;
  disable?: (item: ScriptListType) => boolean;
  onPreviewFn?: (item: ScriptListType) => void;
  bgFn?(item: ScriptListType): string | undefined;
}>();

const emit = defineEmits<{
  (e: "send", item: any): void;
}>();
const terminal = inject<number>("terminal");
const searchInputRef = ref("");
const searchInstructionDataRef = ref();
const inputDomRef = ref<HTMLInputElement>();

const onSearchClick = async () => {
  if (!searchInputRef.value) {
    searchInstructionDataRef.value = [];
    return;
  }
  searchInstructionDataRef.value = await props.headleSearch(
    searchInputRef.value,
  );
};

const onSendClick = (item: any) => emit("send", item);
</script>

<style scoped lang="less"></style>
