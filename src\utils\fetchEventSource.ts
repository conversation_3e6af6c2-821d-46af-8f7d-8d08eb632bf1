type EventHandler<T = Event> = (event: T) => void;

export class FetchEventSource {
  private url: string;
  private options: RequestInit;
  private listeners: Record<string, EventHandler<MessageEvent>[]>;
  private controller: AbortController;

  // 直接赋值的事件处理器
  public onmessage: EventHandler<MessageEvent> | null = null;
  public onopen: EventHandler | null = null;
  public onerror: EventHandler<Event> | null = null;

  constructor(url: string, options: RequestInit = {}) {
    this.url = url;
    this.options = options;
    this.listeners = {
      message: [],
      open: [],
      error: [],
      Msg: [],
    };
    this.controller = new AbortController();
    this.connect();
  }

  // 添加事件监听器
  addEventListener<K extends keyof EventSourceEventMap>(
    type: K,
    listener: EventHandler<EventSourceEventMap[K]>,
  ): void;
  addEventListener(type: string, listener: EventHandler<MessageEvent>): void;
  addEventListener(type: string, listener: EventHandler<MessageEvent>): void {
    if (!this.listeners[type]) {
      this.listeners[type] = [];
    }
    this.listeners[type].push(listener);
  }

  // 移除事件监听器
  removeEventListener<K extends keyof EventSourceEventMap>(
    type: K,
    listener: EventHandler<EventSourceEventMap[K]>,
  ): void;
  removeEventListener(type: string, listener: EventHandler<MessageEvent>): void;
  removeEventListener(
    type: string,
    listener: EventHandler<MessageEvent>,
  ): void {
    if (this.listeners[type]) {
      this.listeners[type] = this.listeners[type].filter((l) => l !== listener);
    }
  }

  // 触发事件

  private dispatchEvent<K extends keyof EventSourceEventMap>(
    type: K,
    event: EventSourceEventMap[K],
  ): void;
  private dispatchEvent(type: string, event: MessageEvent): void;
  private dispatchEvent(type: string, event: MessageEvent): void {
    // 触发直接赋值的事件处理器
    if (type === "message" && this.onmessage) {
      this.onmessage(event);
    } else if (type === "open" && this.onopen) {
      this.onopen(event);
    } else if (type === "error" && this.onerror) {
      this.onerror(event);
    }

    // 触发通过 addEventListener 添加的监听器
    if (this.listeners[type]) {
      this.listeners[type].forEach((listener) => listener(event as any));
    }
  }

  // 连接服务器并处理 SSE 流
  async connect(): Promise<void> {
    try {
      const response = await fetch(this.url, {
        ...this.options,
        headers: {
          ...this.options.headers,
          Accept: "text/event-stream",
        },
        signal: this.controller.signal,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      // 触发 open 事件
      this.dispatchEvent("open", new Event("open"));

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error("ReadableStream not supported");
      }

      const decoder = new TextDecoder();
      let buffer = "";

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });

        let eventEndIndex;
        while ((eventEndIndex = buffer.indexOf("\n\n")) !== -1) {
          const eventData = buffer.slice(0, eventEndIndex);
          buffer = buffer.slice(eventEndIndex + 2);

          const event = this.parseEvent(eventData)!;
          if (event) {
            if (event.type === "message") {
              // 触发 message 事件
              this.dispatchEvent("message", event);
            } else {
              this.dispatchEvent((event as any).type, event);
            }
          }
        }
      }
    } catch (error) {
      const err = error instanceof Error ? error : new Error(String(error));
      const event = new CustomEvent("error", {
        detail: {
          error: err,
        },
      });
      // 触发 error 事件
      this.dispatchEvent("error", event);
    }
  }

  // 解析 SSE 格式的消息
  private parseEvent(eventData: string): MessageEvent | null {
    const lines = eventData.split("\n");
    const event: Record<string, string> = {};

    lines.forEach((line) => {
      const splitIndex = line.indexOf(":");
      const key = line.slice(0, splitIndex);
      const value = line.slice(splitIndex + 1);
      if (key && value) {
        event[key] = value.trim();
      }
    });

    if (Object.keys(event).length > 0) {
      return new MessageEvent(event.event, { data: event.data });
    }

    return null;
  }

  // 关闭连接
  close(): void {
    this.controller.abort();
  }
}
