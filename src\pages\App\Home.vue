<template>
  <div class="h-full flex flex-col bg-[#E6F2FB] pt-10">
    <div
      class="app-search-name-container h-[0.68rem] w-[7rem] mx-auto relative"
    >
      <div
        class="flex bg-[#f9f9f9] text-[0.28rem] h-full items-center rounded-[0.16rem]"
        @click="onShowSearchClick"
      >
        <div
          class="w-[0.32rem] h-[0.32rem] ml-[0.3rem] bg-cover block bg-[url(./assets/top_search.png)]"
        ></div>
        <div class="px-[0.24rem] bg-transparent text-[#abafb1]">搜索</div>
      </div>
    </div>
    <div class="flex flex-1 h-0">
      <Sessions @select-session="onSelectSession" :sse="sseEventEmitter" />
    </div>
  </div>
</template>

<script lang="ts" setup>
import Sessions from "@/components/Sessions.vue";
import { useRoute, useRouter } from "vue-router";
import { useHome } from "../home";

const route = useRoute();
const goSessionDetails = () => {
  const pagePath = `/chat?title=${encodeURIComponent(session.value?.sessionName || "")}`;
  if (route.name === "search") {
    router.replace(pagePath);
  } else {
    router.push(pagePath);
  }
};

const { sseEventEmitter, onSelectSession, session } = useHome(goSessionDetails);
const router = useRouter();

const onShowSearchClick = () => {
  router.push("/search");
};
</script>

<style lang="less" scoped>
.chat {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
}
</style>
