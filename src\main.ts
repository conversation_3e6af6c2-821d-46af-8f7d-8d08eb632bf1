import Vconsole from "vconsole";

import { getSessionList } from "./api/chat";
import { init } from "./initialize/init";
import phoneRemind from "./utils/phoneRemind";

const terminal = init();

if (import.meta.env.MODE === "development" && terminal) {
  // 测试和开发打开，生产不能打开
  new Vconsole();
}

// 先请求一次触发simple-base-sso
// getSessionList({
//   sessionListType: 101,
//   tagCode: "session-list-all",
// })

getSessionList({
  sessionListType: 101,
  tagCode: "session-list-all",
}).then((v) => {
  if (v) {
    if (terminal) {
      import("@/initialize/App/main");
    } else {
      import("@/initialize/Win/main");
    }
    if (phoneRemind.getIsRemind()) {
      phoneRemind.handleCallStatus();
    }
  } else {
    console.error("未登录, auth跳转中...");
  }
});
