<template>
  <div class="chat-app-container flex-1 flex flex-col relative h-full">
    <MsgList
      ref="msgListComp"
      :sse="sse"
      @AIReply="onAIReplyClick"
      @reply-msg="onReplyMsg"
      @revoke-msg="onRevokeMsg"
      @ref-msg="onrefMsg"
      @select-customer="
        (customer) => {
          setSingleSession('customerCode', customer.code);
          setSingleSession('customerMucangId', customer.mucangId);
          setSingleSession('customerWxUnionId', customer.wxUnionId);
          setSingleSession('staffSsoId', '1');
          activeTabRef = 10;
          isMaskRef = true;
        }
      "
    />
    <div
      class="px-26 py-15 bg-[#eff7fc] text-14/19 text-[#888888]"
      v-if="
        replyMsg || (referenceMsg.selected && referenceMsg.content) || refMsg
      "
    >
      <!-- 回复 -->
      <div class="max-w-full flex items-center" v-if="replyMsg">
        <div class="flex-1 flex">
          <div
            class="w-1 border-1 border-solid border-[#c8c8c8] mr-2 flex-1"
          ></div>
          <div class="w-full">
            <span class="text-blue-500">{{ replyMsg.customer?.nickname }}</span
            >：{{ getMsgHint(replyMsg) }}
          </div>
        </div>
        <i
          class="w-21 h-21 flex-[1_auto_auto] cursor-pointer [background-size:100%_100%] bg-[url(./assets/ic_close.png)]"
          @click="onReplyMsg()"
        ></i>
      </div>
      <!-- 快捷 -->
      <div
        class="max-w-full flex items-center"
        v-else-if="referenceMsg.selected && referenceMsg.content"
      >
        <div class="flex-1 flex">
          <div class="w-1 border-1 border-solid border-[#c8c8c8] mr-2"></div>
          <div class="whitespace-normal">{{ referenceMsg.content }}</div>
        </div>
        <div
          class="flex-[1_auto_auto] w-21 h-21 cursor-pointer [background-size:100%_100%] bg-[url(./assets/ic_close.png)]"
          @click="cancelAISuggestion"
        ></div>
      </div>
      <!-- 引用 -->
      <div class="max-w-full flex items-center" v-else-if="refMsg">
        <div class="flex-1 flex">
          <div class="w-1 border-1 border-solid border-[#c8c8c8] mr-2"></div>
          <div class="whitespace-normal">{{ getMsgHint(refMsg) }}</div>
        </div>
        <div
          class="flex-[1_auto_auto] w-21 h-21 cursor-pointer [background-size:100%_100%] bg-[url(./assets/ic_close.png)]"
          @click="onrefMsg()"
        ></div>
      </div>
    </div>
    <div class="tabs">
      <div class="AI">
        <ul class="AI-conteniner">
          <li
            v-for="(item, index) in tabsList"
            :key="index"
            @click="onJump(item)"
            :style="{
              display: item.label ? 'block' : 'none',
            }"
          >
            {{ item.label }}
          </li>
          <li>
            <AppAccountSelect></AppAccountSelect>
          </li>
        </ul>
      </div>
    </div>
    <div></div>
    <div
      class="[border-top:1px_solid_#eee] px-[.3rem] py-[.16rem] bg-[#f2f9ff]"
      ref="inputBoxDomRef"
    >
      <div class="input-box">
        <div class="content-area">
          <AtContainer
            ref="atContainerRef"
            :input-ref="inputRef!"
            v-model="flyMsg"
          >
            <textarea
              ref="inputRef"
              class="textarea outline-none border-none resize-none"
              :readonly="sending"
              :style="{ height: inputHeight }"
              @focus="onTextareaFocus"
              maxlength="1000"
              v-model="flyMsg"
              rows="1"
              placeholder="点击输入文本"
            ></textarea>
          </AtContainer>
        </div>
        <div class="flex items-center ml-[.3rem]">
          <div
            class="emoji-frame w-[0.55rem] h-[0.55rem] flex justify-center items-center"
            v-if="!isJSZP"
            @click="showEmojiRef = !showEmojiRef"
          >
            <div v-html="$body_emoji" class="text-[#333] w-full"></div>
          </div>
          <div v-if="!flyMsg">
            <div
              v-show="!sending"
              class="option-frame w-[0.48rem] h-[0.48rem] border-[0.04rem] border-solid border-[#333333] flex justify-center items-center rounded-full ml-[.4rem]"
            >
              <div
                v-html="$rightAdd"
                class="w-[0.3rem] h-[0.3rem] text-[#333333]"
                @click="openOptionFrameClick"
              ></div>
            </div>
            <div
              v-show="sending"
              class="w-[0.6rem] h-[0.6rem] bg-[url(./assets/top_Refresh.png)] [background-size:100%_100%] animate-spin ml-[.27rem]"
            ></div>
          </div>
          <div
            v-else
            class="but-area transition-all duration-600 ease-in-out"
            :class="
              flyMsg && !sending
                ? 'bg-[#04A5FF] text-white cursor-pointer'
                : 'bg-gray-100 text-gray-500 cursor-not-allowed'
            "
            @click="sendMessage"
          >
            发送{{ sending ? "中..." : "" }}
          </div>
        </div>
      </div>
    </div>
    <div v-show="showOptionFrameRef" ref="iconButtonRef">
      <IconButton @send-message="sendOtherMessage"></IconButton>
    </div>
    <div v-show="showEmojiRef" ref="emojiRef">
      <AppEmoji
        @send-message="sendOtherMessage"
        @insert-emoji="
          (emoji) => {
            atContainerRef!.insertEmoji(emoji.text, emoji.imgUrl);
          }
        "
      ></AppEmoji>
    </div>
  </div>
</template>

<script lang="ts" setup>
import IconButton from "../Common/IconButton/App.vue";
import CustomerDetail from "@/components/ChatQuick/CustomerDetail.vue";
import JXBM from "@/components/ChatQuick/JXBM.vue";
import AppAccountSelect from "../Common/AccountSelect/AppAccountSelect.vue";
import AppEmoji from "../Common/IconButton/AppEmoji.vue";
import MsgList from "./MsgList.vue";
import { computed, h, onMounted, onUnmounted, ref, watchEffect } from "vue";
import { Msg } from "@/api/chat";
import { EventEmitter } from "@/utils/eventEmitter";
import { isJXBM } from "@/utils/helpers";
import { useMsgSender, type Tab } from "./index";
import { getMsgHint } from "@/utils/helpers";
import AtContainer from "./AtContainer.vue";
import { URLParams } from "@/utils/tools";
import { useSessionStore } from "@/store/index";
import { storeToRefs } from "pinia";
import { useRouter } from "vue-router";
import { isJSZP, isParrotSales } from "@/utils/helpers";
import $rightAdd from "@/assets/right-add.svg?raw";
import $body_emoji from "@/assets/body_emoji.svg?raw";

defineProps<{
  sse?: EventEmitter;
}>();

const iconButtonRef = ref<HTMLElement>();
const emojiRef = ref<HTMLElement>();
const { session } = storeToRefs(useSessionStore());
const { setSingleSession } = useSessionStore();
const msgListComp = ref<typeof MsgList>();
const showOptionFrameRef = ref(false);
const showEmojiRef = ref(false);
const inputBoxDomRef = ref();
const tabsTopRef = ref(0);
const router = useRouter();

const {
  flyMsg,
  inputRef,
  inputHeight,
  sending,
  referenceMsg,
  replyMsg,
  atContainerRef,
  refMsg,
  tabsMapper,
  activeTabRef,
  onrefMsg,
  cancelAISuggestion,
  selectReferenceMsg,
  sendMessage,
  onReplyMsg,
  onRevokeMsg,
  sendOtherMessage,
} = useMsgSender((msg) => {
  msgListComp.value!.appendNewMsgAfterSend(msg);
});

// 是否显示遮罩测
const isMaskRef = ref(false);

const tabsList = computed<Tab[]>(() => {
  const allSceneMap: Record<string, Array<keyof typeof tabsMapper>> = {
    "10001": ["Instructions", "QA"],
    "10002": [
      "Instructions",
      "QA",
      "CustomerDetailUserInfo",
      "CustomerDetailOrder",
      "CustomerDetailStudentAnalysis",
      "CustomerDetailTeach",
    ],
    "10006": ["Instructions", "QA"],
    "10007": [
      "WordLibrary",
      "CustomerDetailUserInfo",
      "Instructions",
      "QA",
      "CustomerDetailStudentAnalysis",
      "CustomerDetailTeach",
      "LabelList",
    ],
    "10009": ["Instructions", "QA", "CustomerDetailUserInfo"],
    "10010": ["Instructions", "QA", "CustomerDetailUserInfo"],
    "10011": ["Instructions", "QA", "JXBM", "UserOrders", "IntentionForm"],
  };
  allSceneMap["10013"] =
    allSceneMap["10099"] =
    allSceneMap["10012"] =
      allSceneMap["10007"];
  const tabs = allSceneMap[URLParams.get("bizCode") || "10001"] || ["AIChat"];
  if (session.value!.isGroup) {
    const userOrder = (isJXBM && tabsMapper["UserOrders"]) as Tab;
    return [
      tabsMapper[tabs[0]],
      tabsMapper[tabs[1]],
      tabsMapper["MemberList"],
      {
        id: 10,
        label: "学员档案",
        go: () => {
          if (!isJXBM) {
            router.push({
              name: "CustomerDetail",
              params: {
                type: "userinfo",
              },
            });
          } else {
            router.push({
              name: "JXBM",
            });
          }
        },
        render() {
          if (!isJXBM) {
            return h(CustomerDetail);
          } else {
            return h(JXBM);
          }
        },
      },
      userOrder,
    ];
  } else {
    return tabs.map((tab) => {
      return tabsMapper[tab];
    });
  }
});

const AIReplyTabList = computed<Tab>(() => {
  if (isParrotSales) {
    return tabsMapper.AIScript;
  } else {
    return tabsMapper.AI;
  }
});
const onAIReplyClick = (msg?: Msg) => {
  selectReferenceMsg(msg);
  AIReplyTabList.value.go && AIReplyTabList.value.go(msg);
};
const onJump = (tab: Tab) => {
  console.log(tab);
  if (tab.go) {
    tab.go();
  }
  if (tab.onEmitterFn) {
    tab.onEmitterFn();
  }
};

// 打开更多框
const openOptionFrameClick = () => {
  showOptionFrameRef.value = !showOptionFrameRef.value;
};
// 关闭更多框
const closeOptionFrame = (e: Event) => {
  const target = e.target as HTMLElement;
  if (
    showOptionFrameRef.value &&
    iconButtonRef.value &&
    iconButtonRef.value.contains(target) &&
    target.closest(".option-frame") === null
  ) {
    return;
  }
  showOptionFrameRef.value = false;
};

// 关闭表情框
const closeEmoji = (e: Event) => {
  const target = e.target as HTMLElement;
  if (
    showEmojiRef.value &&
    emojiRef.value &&
    emojiRef.value.contains(target) &&
    target.closest(".emoji-frame") === null
  ) {
    return;
  }
  showEmojiRef.value = false;
};

const onTextareaFocus = () => {
  setTimeout(() => {
    msgListComp.value!.autoScrollBottom(false);
  }, 100);
};
onMounted(() => {
  document.body.addEventListener("click", closeOptionFrame, true);
  document.body.addEventListener("click", closeEmoji, true);
  watchEffect(() => {
    tabsTopRef.value =
      inputBoxDomRef.value.offsetHeight + inputBoxDomRef.value.offsetHeight / 2;
  });
});
onUnmounted(() => {
  document.body.removeEventListener("click", closeEmoji, true);
  document.body.removeEventListener("click", closeOptionFrame, true);
});
</script>

<style lang="less" scoped>
.chat-app-container {
  background-color: #fff;

  .input-box {
    width: 100%;
    position: relative;
    left: 0;
    bottom: 0;
    padding: 10px 0;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .content-area {
      flex: 1 1 auto;

      .have-content-icon {
        max-width: 200px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        padding: 0 5px;
        display: flex;
        align-items: center;
        font-size: 0.3rem;
        background-color: rgb(229, 231, 235);
        border-radius: 5px;
      }

      .textarea {
        display: flex;
        flex-direction: column;
        justify-content: center;
        width: 100%;
        font-size: 0.28rem;
        padding: 9px 10px;
        max-height: 1.5rem;
        min-height: 0.76rem;
        border-radius: 0.16rem;
      }
    }

    .but-area {
      margin-left: 6px;
      display: flex;
      flex-direction: column;
      place-self: end;
      font-size: 0.3rem;
      padding: 3px 20px;
      border-radius: 5px;
    }
  }
}

.tabs {
  background-color: #f2faff;
  padding: 10px 0;
}

.self-center {
  font-size: 0.3rem;
  text-align: center;
}

.suggestionsQK {
  font-size: 0.35rem;
}

.AI-conteniner {
  width: 100vw;
  display: flex;
  font-size: 0.23rem;
  overflow-x: auto;
  white-space: nowrap;
  padding-right: 10px;

  &::-webkit-scrollbar {
    display: none;
  }

  li {
    margin-left: 10px;
    border: 0.02rem solid #d7d7d7;
    padding: 8px;

    border-radius: 5px;
  }
}
</style>
