<template>
  <div
    @contextmenu="handleContextMenu"
    @mousedown="handleMouseDown"
    @mousemove="handleMouseUp"
    @mouseup="handleMouseUp"
    @touchstart="handleMouseDown"
    @touchmove="handleMouseUp"
    @touchend="handleMouseUp"
    class="select-none"
  >
    <slot></slot>
    <Teleport to="body">
      <div
        v-if="showMenu"
        class="fixed zIndex rounded-5 border border-[#cccfd0] bg-white shadow-lg max-w-180 p-2 space-y-2"
        :style="{ left: x + 'px', top: y + 'px' }"
        ref="contextMenuRef"
      >
        <div
          :class="terminal ? 'text-[.4rem]/[.8rem]' : 'text-12/34'"
          class="cursor-pointer pl-12 pr-16 hover:bg-[#e6f2fb] hover:text-[#04A5FF] text-[#464646] whitespace-nowrap text-ellipsis overflow-hidden flex items-center"
          @click="handleClick(item)"
          v-for="(item, i) in menu"
          v-show="!item.disabled?.()"
          :key="i"
        >
          <div
            class="w-21 h-21 mr-4"
            v-html="item.icon()"
            v-if="item.icon"
          ></div>
          <div class="w-21 h-21 mr-4" v-else-if="hasAnyIcon"></div>
          <div>{{ item.label() }}</div>
        </div>
      </div>
    </Teleport>
  </div>
</template>

<script lang="ts" setup>
import { ref, nextTick, computed, onMounted, onUnmounted, inject } from "vue";

export interface MenuItem {
  label: () => string;
  icon?: () => string;
  disabled?: () => boolean;
  onClick?: () => void;
}

const props = defineProps<{
  disabled?: boolean;
  menu: MenuItem[];
}>();
const emit = defineEmits<{
  (e: "select", item: MenuItem): void;
}>();
const terminal = inject<number>("terminal");
const hasAnyIcon = computed(() => props.menu.some((item) => item.icon));

const { handleMouseDown, handleMouseUp } = useLongTap((ev) =>
  handleContextMenu(ev),
);
const { handleContextMenu, contextMenuRef, showMenu, x, y } = useContextMenu();

// 菜单的点击事件
const handleClick = (item: MenuItem) => {
  // 选中菜单后关闭菜单
  showMenu.value = false;
  item.onClick?.();
  // 并返回选中的菜单
  emit("select", item);
};

function useLongTap(onlongtap: (ev: MouseEvent | TouchEvent) => void) {
  let timer: number | null = null;

  const handleMouseDown = (ev: MouseEvent | TouchEvent) => {
    // 开始计时
    timer = window.setTimeout(() => {
      // 在这里执行长按后的逻辑
      console.log("长按事件触发");
      onlongtap(ev);
    }, 800); // 800毫秒后触发长按事件
  };

  const handleMouseUp = () => {
    // 停止计时器
    if (timer) {
      clearTimeout(timer);
      timer = null;
    }
  };

  return {
    handleMouseDown,
    handleMouseUp,
  };
}

function useContextMenu() {
  const contextMenuRef = ref<HTMLElement | null>(null);
  const showMenu = ref(false);
  const x = ref(0);
  const y = ref(0);
  function setPosition(e: MouseEvent | TouchEvent) {
    const menu = contextMenuRef.value!;
    const menuHeight = menu.offsetHeight;
    const menuWidth = menu.offsetWidth;
    const windowHeight = window.innerHeight;
    const windowWidth = window.innerWidth;
    const top = e instanceof MouseEvent ? e.clientY : e.touches[0].clientY;
    const left = e instanceof MouseEvent ? e.clientX : e.touches[0].clientX;
    if (top + menuHeight > windowHeight) {
      y.value = windowHeight - menuHeight;
    } else {
      y.value = top;
    }
    if (left + menuWidth > windowWidth) {
      x.value = windowWidth - menuWidth;
    } else {
      x.value = left;
    }
  }
  const handleContextMenu = (e: MouseEvent | TouchEvent) => {
    e.preventDefault(); // 阻止浏览器的默认行为
    e.stopPropagation(); // 阻止冒泡
    if (props.disabled) {
      return;
    }
    showMenu.value = true;
    nextTick(() => {
      setPosition(e);
    });
  };
  const closeMenu = () => {
    showMenu.value = false;
  };
  onMounted(() => {
    // 触发 window 点击事件的时候执行函数
    window.addEventListener("click", closeMenu, true);
    // 处理 window 的 contextmenu 事件，用来关闭之前打开的菜单
    window.addEventListener("contextmenu", closeMenu, true);
  });
  onUnmounted(() => {
    window.removeEventListener("click", closeMenu, true);
    window.removeEventListener("contextmenu", closeMenu, true);
  });

  return { handleContextMenu, contextMenuRef, showMenu, x, y };
}
</script>
<style lang="less" scoped>
.zIndex {
  z-index: 1;
}
</style>
