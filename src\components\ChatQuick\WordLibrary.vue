<!-- 话术库 -->
<template>
  <div
    class="relative w-full h-full"
    :class="terminal ? 'overflow-auto h-screen' : ''"
  >
    <div
      class="bg-[#E6F2FB] rounded-12 border-3 border-solid border-[#fff] box-border flex flex-col flex-1"
    >
      <div class="head flex pt-11 pb-16 items-center">
        <div class="items-center flex">
          <i class="w-3 h-14 bg-[#04a5ff] rounded-r-2 mr-10"></i>
          <span class="text-14/20 font-bold text-[rgba(0,0,0,0.85)]"
            >话术库</span
          >
        </div>
        <div class="flex-1 text-center"></div>
        <div class="w-50 relative"></div>
      </div>
      <div class="px-14 flex flex-col flex-1">
        <Search
          :headleSearch="onSearchClick"
          @send="onSendClick"
          :bg-fn="headleBgColor"
        >
          <template #title="{ item }">
            <span
              v-html="isInstructionList(item) && item.instructionName"
            ></span>
            <span class="text-[#B6B7B9] ml-5">
              (共{{ isInstructionList(item) && item.commands.length }}条)</span
            >
          </template>
          <template #default="{ item }">
            <div
              class="flex flex-col items-end mt-10 py-5 px-20 text-13 gap-y-10"
            >
              <div
                class="bg-[#00a3ff] rounded-tl-12 px-10 rounded-br-12 py-5 rounded-tr-none rounded-bl-12 text-[#ffffff]"
                v-for="detailsItem in (isInstructionList(item) ? item : {})
                  .commands"
              >
                {{ detailsItem.commandDesc }}
              </div>
            </div>
          </template>
        </Search>
        <GroupList
          title="话术分组"
          :groupDataList="groupListRef"
          :select-group="categoryTypeRef"
          @onGroupSelect="onGroupSelect"
        ></GroupList>
        <ScriptList
          v-if="instructionDataRef"
          :dataList="
            instructionDataRef?.filter(
              (it) => it.categoryType === categoryTypeRef,
            )[0].belong
          "
          :bg-fn="headleBgColor"
          @send="onSendClick"
        >
          <template #title="{ item }">
            <span
              v-html="isInstructionList(item) && item.instructionName"
            ></span>
            <span class="text-[#B6B7B9] ml-5">
              (共{{ isInstructionList(item) && item.commands.length }}条)</span
            >
          </template>
          <template #default="{ item }">
            <div
              class="flex flex-col items-end mt-10 py-5 px-20 text-13 gap-y-10"
            >
              <div
                class="bg-[#00a3ff] rounded-tl-12 px-10 rounded-br-12 py-5 rounded-tr-none rounded-bl-12 text-[#ffffff]"
                v-for="detailsItem in (isInstructionList(item) ? item : {})
                  .commands"
              >
                {{ detailsItem.commandDesc }}
              </div>
            </div>
          </template>
        </ScriptList>
      </div>
    </div>
    <Loading :isLoading="loadingRef" :loadingText="loadingTextRef"></Loading>
  </div>
</template>

<script setup lang="ts">
import { inject, onMounted, ref } from "vue";
import {
  getInstructionList,
  InstructionSearchType,
  getCategoryList,
  InstructionList,
  CategoryList,
} from "@/api/instruct";
import { execInstruction } from "@/api/parrot";
import { makeToast } from "@/utils/dom";
import ScriptList from "./ScriptList.vue";
import { useSessionStore } from "@/store/index";
import { storeToRefs } from "pinia";
import { useRouter } from "vue-router";
import Loading from "../Loading.vue";
import Search from "./Common/Search.vue";
import GroupList from "./Common/GroupList.vue";
import { ScriptListType, isInstructionList } from "./Common/type";
import { WordLibraryBgEnum } from "@/utils/constant";

interface InstructionData {
  categoryName: string;
  categoryType: string;
  belong: InstructionList["itemList"];
}
const { session } = storeToRefs(useSessionStore());
const loadingRef = ref(true);
const loadingTextRef = ref<string>("");
const instructionDataRef = ref<InstructionData[]>();
const categoryTypeRef = ref<string>("1");
const groupListRef = ref<CategoryList["itemList"]>([]);
const searchInputRef = ref<string>();
const router = useRouter();
const terminal = inject<number>("terminal");

onMounted(async () => {
  reset();
  loadingTextRef.value = "加载中...";
  loadingRef.value = true;
  const res = await Promise.all([
    getCategoryData(),
    getInstructionData(),
  ]).finally(() => {
    loadingRef.value = false;
    loadingTextRef.value = "";
  });
  groupListRef.value = res[0].itemList;
  const { instructionData, type } = handleData(res);
  instructionDataRef.value = instructionData;
  categoryTypeRef.value = type;
});

/**
 * 处理数据
 */
const handleData = (
  data: [CategoryList, InstructionList],
): { instructionData: InstructionData[]; type: string } => {
  const instructionData = data[0].itemList.map((item) => {
    return {
      categoryName: item.categoryName,
      categoryType: item.categoryType,
      belong: data[1].itemList.filter(
        (it) => item.categoryName === it.categoryName,
      ),
    };
  });
  console.log(instructionData);

  const type = instructionData[0].categoryType;
  if (data[1].itemList.length <= 0) {
    return {
      instructionData: [],
      type,
    };
  }
  return {
    instructionData,
    type,
  };
};

const headleBgColor = (item: ScriptListType) => {
  if (isInstructionList(item)) {
    return WordLibraryBgEnum[item.renderTag];
  }
};

/**
 * 获取话术列表
 * @param data
 */
const getInstructionData = async (
  data: InstructionSearchType = { categoryName: "", instructionName: "" },
) => {
  const res = await getInstructionList({
    categoryName: data.categoryName,
    instructionName: data.instructionName,
  });
  return res;
};
/**
 * 获取分组列表
 */
const getCategoryData = async () => {
  const res = await getCategoryList();
  return res;
};
/**
 * 搜索按钮
 */
const onSearchClick = async (searchInput: string) => {
  loadingRef.value = true;
  loadingTextRef.value = "搜索中...";
  const res = await getInstructionData({
    instructionName: searchInput,
  }).finally(() => {
    loadingRef.value = false;
    loadingTextRef.value = "";
  });
  const reg = new RegExp(searchInput, "ig");
  const data = res.itemList.map((item) => {
    return {
      ...item,
      instructionName: item.instructionName.replace(
        reg,
        `<span class="text-[#04A5FF]">${searchInput}</span>`,
      ),
    };
  });
  return data;
};
/**
 * 发送按钮
 */
const onSendClick = async (item: ScriptListType) => {
  if (isInstructionList(item)) {
    loadingRef.value = true;
    loadingTextRef.value = "发送中...";
    const { value } = (await execInstruction({
      sessionKey: session.value!.sessionKey,
      msgCode: session.value?.latestMsg?.msgCode!,
      instructionKeys: item.instructionKey,
    }).finally(() => {
      loadingTextRef.value = "";
      loadingRef.value = false;
      terminal === 1 && router.back();
    })) as { value: boolean };
    value && makeToast("发送成功", undefined, 500);
  }
};

/**
 * 选择
 * @param item
 */
const onGroupSelect = (item: string) => {
  categoryTypeRef.value = item;
};

const reset = () => {
  searchInputRef.value = "";
  instructionDataRef.value = [];
  categoryTypeRef.value = "";
};

defineExpose({
  reset,
});
</script>

<style scoped lang="less"></style>
