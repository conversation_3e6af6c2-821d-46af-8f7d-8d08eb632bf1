import { onUnmounted } from "vue";

type EventHandler = (...args: any[]) => void;

export class EventEmitter {
  private events: { [eventName: string]: EventHandler[] };

  constructor() {
    this.events = {};
  }

  on(eventName: string, callback: EventHandler): void {
    if (!this.events[eventName]) {
      this.events[eventName] = [];
    }
    this.events[eventName].push(callback);
  }

  emit(eventName: string, ...args: any[]): void {
    if (this.events[eventName]) {
      this.events[eventName].forEach((callback) => callback(...args));
    }
  }

  off(eventName: string, callback: EventHandler): void {
    if (this.events[eventName]) {
      this.events[eventName] = this.events[eventName].filter(
        (cb) => cb !== callback,
      );
    }
  }

  once(eventName: string, callback: EventHandler): void {
    const onceCallback = (...args: any[]) => {
      callback(...args);
      this.off(eventName, onceCallback);
    };
    this.on(eventName, onceCallback);
  }
}

export function useEventEmitter(
  eventEmitter: EventEmitter,
  eventName: string,
  callback: EventHandler,
): void {
  eventEmitter.on(eventName, callback);
  onUnmounted(() => {
    eventEmitter.off(eventName, callback);
  });
}
