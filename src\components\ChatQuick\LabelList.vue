<!-- 标签列表 -->
<template>
  <div class="min-h-full py-10 flex flex-col bg-[#e6f2fb] rounded-12">
    <div class="flex items-center">
      <i class="w-3 h-14 bg-[#04a5ff] rounded-r-2 mr-10"></i>
      <span class="text-14/20 font-bold text-[rgba(0,0,0,0.85)]">标签列表</span>
    </div>
    <div
      v-if="sessionTagListRef && sessionTagListRef.length > 0"
      class="mx-10 mt-10 text-[#333333]"
      :class="{
        'text-14': !terminal,
        'text-[.32rem]': terminal,
      }"
    >
      <div class="" v-for="(item, index) in sessionTagListRef" :key="index">
        <div class="flex justify-between items-stretch">
          <div
            class="bg-[#F6F9FB] break-all px-10 w-111 py-5 border-1 border-solid border-[#e6eef4] flex items-center"
          >
            {{ item.tagName }}
          </div>
          <div
            class="flex-1 min-w-[0] break-all bg-[#ffffff] border-1 border-solid border-[#e6eef4] [border-left:0] px-10 flex items-center"
          >
            {{ item.tagValue }}
          </div>
        </div>
      </div>
    </div>
    <div
      v-else-if="!isLoadingRef"
      class="mt-10 text-center"
      :class="{
        'text-20/20': !terminal,
        'text-[.4rem]/[.4rem]': terminal,
      }"
    >
      暂无
    </div>
    <LoadingComp :isLoading="isLoadingRef"></LoadingComp>
  </div>
</template>

<script setup lang="ts">
import { getSessionTags, SessionTags } from "@/api/parrot";
import { useSessionStore } from "@/store/index";
import { makeToast } from "@/utils/dom";
import { storeToRefs } from "pinia";
import { inject, ref, watch } from "vue";
import LoadingComp from "@/components/Loading.vue";

const { session } = storeToRefs(useSessionStore());

const terminal = inject("terminal");

const sessionTagListRef = ref<SessionTags[]>();
const isLoadingRef = ref(false);
/**
 * 获取会话标签列表
 */
const getSessionTagsList = async () => {
  sessionTagListRef.value = [];
  if (session.value && session.value.sessionKey) {
    isLoadingRef.value = true;
    const res = await getSessionTags(session.value.sessionKey).finally(() => {
      isLoadingRef.value = false;
    });
    sessionTagListRef.value = res.itemList;
  } else {
    makeToast("未获取到sessionKey");
  }
};

watch(() => session.value!, getSessionTagsList, {
  immediate: true,
});
</script>

<style scoped lang="less"></style>
