<template>
  <div class="h-[100vh] flex flex-col bg-[#E6F2FB]">
    <div class="flex flex-1 h-full">
      <ChatApp v-if="session" class="chat" :sse="sseEventEmitter" />
    </div>
  </div>
</template>

<script setup lang="ts">
import ChatApp from "@/components/Chat/App.vue";

import { useSessionStore, useSseEventEmitterStore } from "@/store/index";
import { storeToRefs } from "pinia";

const { session } = storeToRefs(useSessionStore());
const { sseEventEmitter } = storeToRefs(useSseEventEmitterStore());
</script>

<style scoped></style>
