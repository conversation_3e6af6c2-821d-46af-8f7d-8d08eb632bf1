<template>
  <div
    class="min-h-full flex flex-col gap-y-8 box-border"
    :class="terminal ? 'overflow-auto h-screen' : ''"
  >
    <div class="bg-[#e6f2fb] rounded-14 flex items-center px-16 py-15">
      <label>
        <div :class="terminal ? 'w-[1.2rem] h-[1.2rem]' : 'w-50 h-50'">
          <img
            :src="session!.avatar"
            alt=""
            class="w-full h-full rounded-full cursor-pointer"
          />
          <input
            type="file"
            accept="image/*"
            class="w-0 h-0 block"
            @change="onUpdateAvatarchange"
          />
        </div>
      </label>
      <div class="flex-1 ml-12 text-0">
        <span
          v-if="!state.showGroupName"
          :class="terminal ? 'text-[.4rem] ' : 'text-16'"
          class="text-16 font-semibold"
          >{{ session!.sessionName }}</span
        >
        <input
          v-else
          type="text"
          name=""
          id=""
          class="px-5 border-1 border-solid rounded-8 border-[#cccfd0] outline-none w-full"
          :class="terminal ? 'h-[.7rem] text-[.3rem] ' : 'h-33 text-13'"
          :value="session!.sessionName"
          placeholder="请输入群名"
          ref="groupNameDomRef"
        />
      </div>
      <div
        :class="terminal ? 'w-[.36rem] ' : 'w-12 h-13'"
        v-if="!state.showGroupName"
      >
        <img
          src="@/assets/ic_bj.png"
          alt=""
          class="w-full h-full cursor-pointer"
          @click="onShowGroupNameClick"
        />
      </div>
      <div
        v-else
        :class="
          terminal
            ? 'w-[.9rem] h-[.5rem] text-[.3rem]/[.5rem] rounded-5'
            : 'w-40 h-25 text-12/25 rounded-5'
        "
        class="bg-[#005EFF] text-[#fff] text-center cursor-pointer ml-10"
        @click="onSaveGroupNameClick"
      >
        保存
      </div>
    </div>
    <div class="bg-[#e6f2fb] rounded-14 pb-17">
      <div class="items-center flex h-50">
        <i
          class="bg-[#04a5ff] rounded-r-2 mr-10"
          :class="terminal ? 'w-[.05rem] h-[.5rem]' : 'w-3 h-14'"
        ></i>
        <span
          :class="terminal ? 'text-[.3rem]' : 'text-14/20'"
          class="font-bold text-[rgba(0,0,0,0.85)] flex-1"
          >群公告</span
        >
        <div
          :class="terminal ? 'w-[.36rem] ' : 'w-12 h-13'"
          class="mr-16"
          v-if="!state.showNotice"
        >
          <img
            src="@/assets/ic_bj.png"
            alt=""
            class="w-full h-full cursor-pointer"
            @click="onShowNoticeClick"
          />
        </div>
        <div
          v-else
          :class="
            terminal
              ? 'w-[.9rem] h-[.5rem] text-[.3rem]/[.5rem] rounded-5'
              : 'w-40 h-25 text-12/25 rounded-5'
          "
          class="bg-[#005EFF] text-[#fff] text-center cursor-pointer mr-15"
          @click="onSaveNoticeClick"
        >
          保存
        </div>
      </div>
      <div class="px-14 h-125 overflow-auto text-0">
        <span class="text-13 text-[#333333]" v-if="!state.showNotice">
          {{ session!.groupBulletin }}
        </span>
        <textarea
          v-else
          class="text-13 p-7 outline-none border-1 border-solid rounded-8 border-[#cccfd0] resize-none h-full w-full bg-[#ffffff] text-[#333333]"
          placeholder="请输入公告"
          ref="noticeDomRef"
          :value="session!.groupBulletin"
        ></textarea>
      </div>
    </div>
    <div class="bg-[#e6f2fb] rounded-14 flex-1 min-h-250">
      <div class="items-center h-50 flex">
        <i
          class="bg-[#04a5ff] rounded-r-2 mr-10"
          :class="terminal ? 'w-[.05rem] h-[.5rem]' : 'w-3 h-14'"
        ></i>
        <span
          :class="terminal ? 'text-[.3rem]' : 'text-14/20'"
          class="font-bold text-[rgba(0,0,0,0.85)] flex-1"
          >群成员</span
        >
      </div>
      <div
        class="flex items-center px-14 h-54 self-start"
        v-for="member in groupMembersListRef"
      >
        <ContextMenu :menu="getContextMenuItems(member)">
          <div
            :class="terminal ? 'w-[.8rem] h-[.8rem]' : 'w-40 h-40'"
            class="bg-cover rounded-full"
            :style="{ backgroundImage: `url(${member.avatar})` }"
          />
        </ContextMenu>
        <span :class="terminal ? 'text-[.4rem]' : ''" class="ml-20 flex-1">{{
          member.nickname
        }}</span>
        <div
          :class="terminal ? 'text-[.2rem]' : 'text-11'"
          class="text-[#04a5ff] py-2 px-7 bg-[#d1efff] box-border rounded-10"
        >
          {{ groupRole[member.role] }}
        </div>
        <div
          :class="terminal ? 'text-[.2rem]' : 'text-11'"
          class="text-[#FF4A40] py-2 px-7 box-border rounded-10"
        >
          {{ member.muted ? "禁言中" : "" }}
        </div>
      </div>
      <!-- <div
        class="flex items-center px-14 h-54 self-start cursor-pointer"
        @click="onShowAddClick"
      >
        <div
          :class="terminal ? 'w-[.8rem] h-[.8rem]' : 'w-40 h-40'"
          class="bg-[#dce6f2] rounded-full relative"
        >
          <img
            src="@/assets/right-add.svg"
            alt=""
            class="w-16 h-16 absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2"
          />
        </div>
        <span class="ml-20" :class="terminal ? 'text-[.4rem]' : ''">
          添加成员
        </span>
      </div> -->
      <div
        class="h-155 bg-[#ffffff] border-1 border-solid border-[#cccfd0] rounded-11 px-14"
        ref="addFrameDomRef"
        v-if="state.showAdd"
      >
        <div
          class="py-14 text-center w-full text-[#333333]"
          :class="terminal ? 'text-[.32rem]' : 'text-14'"
        >
          输入木仓ID
        </div>
        <div class="border-1 text-0">
          <input
            type="text"
            placeholder="多个木仓ID用，号隔开"
            ref="muCangInputRef"
            class="h-39 bg-[#f9f9f9] rounded-10 outline-none border-none w-full px-14 text-14"
            v-model="state.muCangIdValue"
          />
        </div>
        <div class="flex justify-center mt-11">
          <button
            :class="terminal ? 'text-[.32rem]' : ''"
            class="bg-[#ff822d] text-[#ffffff] rounded-9 h-39 w-134"
            @click="onAddMemberClick"
          >
            添加至群聊
          </button>
        </div>
      </div>
    </div>
    <div class="flex-1">
      <PhoneRemindComp
        :groupMembersList="groupMembersListRef"
      ></PhoneRemindComp>
    </div>
  </div>
</template>
<script lang="ts" setup>
import {
  inject,
  nextTick,
  onMounted,
  onUnmounted,
  reactive,
  ref,
  watch,
} from "vue";
import ContextMenu from "../ContextMenu.vue";
//import { EventEmitter } from "@/utils/eventEmitter";
import { makeToast } from "@/utils/dom";
import {
  updateGroupName,
  updateBulletin,
  getGroupMember,
  GroupMemberList,
  forbidSendMsg,
  updateGroupAvatar,
} from "@/api/groupChat";
import { getSessionDetail } from "@/api/chat";
import { prompt } from "@/utils/dom";
import { groupRole } from "@/utils/constant";
import { useSessionStore } from "@/store/index";
import { storeToRefs } from "pinia";
import { EventEmitter } from "@/utils/eventEmitter";
import PhoneRemindComp from "./PhoneRemind.vue";
// @ts-ignore
import MCSUpload from "@simplex/simple-secure-upload";

// 比如在有上传功能的页面或弹窗初始化时就单独调用以下代码
import { Store } from "@simplex/simple-base-sso";

Store.extend({
  apiCors: false,
  autoLogin: true,
})
  .create({
    url: "https://cyclops.mucang.cn/api/admin/upload-file/upload.htm?from=mcs-pre-request",
  })
  .request();

const { session } = storeToRefs(useSessionStore());
const { setSingleSession } = useSessionStore();
const terminal = inject<0 | 1>("terminal");
const emitter = inject<EventEmitter>("emitter")!;
// const emitter = inject<EventEmitter>("emitter")!;
const addFrameDomRef = ref<HTMLElement>();
const groupNameDomRef = ref<HTMLInputElement>();
const noticeDomRef = ref<HTMLInputElement>();
const muCangInputRef = ref<HTMLElement>();
const groupMembersListRef = ref<GroupMemberList[]>([]);

const state = reactive({
  showAdd: false,
  muCangIdValue: "",
  showGroupName: false,
  showNotice: false,
});

const getContextMenuItems = (member: GroupMemberList) => {
  return [
    // {
    //   label: () => "查看学员档案",
    //   onClick() {
    //     emit("selectCustomer", member);
    //   },
    // },
    // {
    //   label: () => "设为管理员",
    //   onClick() {
    //     console.log("点击");
    //   },
    // },
    {
      label: () => (member.muted ? "解禁" : "禁言"),
      disabled: () => !member.customerId,
      onClick() {
        prompt({
          title: member.muted ? "确定解禁吗？" : "确定禁言吗？",
          terminal: terminal,
        }).then(async () => {
          await forbidSendMsg({
            sessionKey: session.value!.sessionKey,
            customerIdList: member.customerId,
            muteTime: member.muted ? 0 : -1,
          });
          makeToast(member.muted ? "成功解禁" : "成功禁言");
          for (let i = 0; i < groupMembersListRef.value.length; i++) {
            if (groupMembersListRef.value[i].customerId === member.customerId) {
              groupMembersListRef.value[i].muted =
                !groupMembersListRef.value[i].muted;
              break;
            }
          }
        });
      },
    },
    // {
    //   label: () => "踢出群聊",
    //   async onClick() {
    //     const { value } = await kickOffGroupMember({
    //       sessionKey: session.sessionKey,
    //       mucangId: member.customerId,
    //     });
    //     if (value) {
    //       makeToast("成功踢出");
    //     }
    //     // 等待一下，服务器有延迟
    //     setTimeout(() => {
    //       emitter.emit("refreshGroupMembers", session);
    //     }, 1500);
    //   },
    // },
  ];
};
/**
 * 点击打开修改群名输入框
 */
const onShowGroupNameClick = () => {
  state.showGroupName = true;
  nextTick(() => {
    groupNameDomRef.value?.focus();
  });
};
/**
 * 点击保存修改群名
 */
const onSaveGroupNameClick = () => {
  const target = groupNameDomRef.value;
  if (groupNameDomRef.value?.value === session.value!.sessionName) {
    state.showGroupName = false;
    return;
  }
  if (!target?.value || target.value.length > 20) {
    makeToast("请输入20个字以内的群名");
    return;
  }
  prompt({
    title: "确认修改群名称吗?",
    terminal: terminal,
  }).then(async () => {
    await updateGroupName({
      sessionKey: session.value!.sessionKey,
      groupName: target.value,
    }).catch(() => {
      makeToast("修改群名称失败");
    });
    makeToast("修改群名称成功");
    setSingleSession("sessionName", target.value);
    emitter.emit("selectSession", session.value);
  });
  state.showGroupName = false;
};
/**
 * 点击打开修改群公告输入框
 */
const onShowNoticeClick = () => {
  state.showNotice = true;
  nextTick(() => {
    noticeDomRef.value?.focus();
  });
};
/**
 * 点击保存修改群公告
 */
const onSaveNoticeClick = () => {
  const target = noticeDomRef.value as HTMLInputElement;
  if (target.value === session.value!.groupBulletin) {
    state.showNotice = false;
    return;
  }
  prompt({
    title: "确认修改群公告吗?",
    terminal: terminal,
  }).then(async () => {
    // 等待请求
    await updateBulletin({
      sessionKey: session.value!.sessionKey,
      groupBulletin: target.value,
    }).catch(() => {
      makeToast("修改群公告失败");
    });
    makeToast("修改群公告成功");
    setSingleSession("groupBulletin", target.value);
  });
  state.showNotice = false;
};

/**
 * 修改群头像
 */
const onUpdateAvatarchange = async (e: Event) => {
  const target = e.target as HTMLInputElement;
  if (!target.files || !target.files[0]) {
    return;
  }
  const file = target.files[0];
  target.value = "";
  const encodedData = await uploadSingleFile(file);
  const res = await updateGroupAvatar({
    sessionKey: session.value!.sessionKey,
    faceUrl: encodedData,
  }).catch(() => {
    makeToast("修改群头像失败");
  });
  if (res?.value) {
    const sessionDetail = await getSessionDetail({
      sessionKey: session.value!.sessionKey,
    });
    setSingleSession("avatar", sessionDetail.avatar);
    makeToast("修改群头像成功");
  }
};

/** 上传文件，返回encodedData */
function uploadSingleFile(
  file: File | { fileName: string; data: string },
): Promise<string> {
  return (
    new MCSUpload({
      admin: true,
      appSpaceId: "23f5443423f5341c1dc8",
      // @ts-ignore
      files: [file],
      chunkSize: 1.5,
      useMd5: false,
    })
      // @ts-ignore
      .then((sRes: any) => {
        console.log("成功", sRes);
        return sRes[0].encodedData;
      })
      .catch((fRes: any) => {
        console.log("失败", fRes);
      })
  );
}

const onAddMemberClick = () => {
  // 等待请求
  console.log(state.muCangIdValue);
  state.muCangIdValue = "";
};

// const onShowAddClick = () => {
//   state.showAdd = true;
//   nextTick(() => {
//     muCangInputRef.value?.focus();
//   });
// };

const clickOutSide = (e: Event) => {
  const target = e.target as HTMLElement;
  if (
    state.showAdd &&
    addFrameDomRef.value &&
    addFrameDomRef.value.contains(target)
  ) {
    return;
  }
  state.showAdd = false;
};
onMounted(() => {
  document.addEventListener("click", clickOutSide, true);
});
onUnmounted(() => {
  document.removeEventListener("click", clickOutSide, true);
});

watch(
  () => session.value!,
  async () => {
    groupMembersListRef.value = (
      await getGroupMember(session.value!.sessionKey)
    ).itemList;
  },
  {
    immediate: true,
  },
);
</script>
