<!-- 用户订单 -->
<template>
  <div class="px-14">
    <div v-if="orderListRef && orderListRef.length > 0">
      <div
        class="mt-17 mb-17"
        v-for="(item, index) in orderListRef"
        :key="item.id"
      >
        <p
          class="font-bold text-[#333333]"
          :class="{
            'text-15': !terminal,
            'text-[.32rem]': terminal,
          }"
        >
          订单{{ index + 1 }}:
        </p>
        <div
          class="bg-[#fff] mt-8 p-13 rounded-5"
          :class="{
            'text-14': !terminal,
            'text-[.3rem]': terminal,
          }"
        >
          <div class="flex justify-between items-center">
            <p class="text-[#a0a0a0] w-72 mr-10">商品名称:</p>
            <p class="text-[#565656] flex-1 flex justify-end">
              {{ item.courseName || "暂无" }}
            </p>
          </div>
          <div class="flex justify-between items-center mt-8">
            <p class="text-[#a0a0a0] w-72 mr-10">驾校简称:</p>
            <p class="text-[#565656] flex-1 flex justify-end">
              {{ item.jiaxiaoName || "暂无" }}
            </p>
          </div>
          <div class="flex justify-between items-center mt-8">
            <p class="text-[#a0a0a0] w-72 mr-10">所属城市:</p>
            <p class="text-[#565656] flex-1 flex justify-end">
              {{ item.cityName || "暂无" }}
            </p>
          </div>
          <div class="flex justify-between items-center mt-8">
            <p class="text-[#a0a0a0] w-72 mr-10">商品价格:</p>
            <p class="text-[#565656] flex-1 flex justify-end">
              {{ item.orderAmount ? `￥${item.orderAmount}` : "暂无" }}
            </p>
          </div>
          <div class="flex justify-between items-center mt-8">
            <p class="text-[#a0a0a0] w-72 mr-10">订单状态:</p>
            <p class="text-[#04A5FF] flex-1 flex justify-end">
              {{ orderStatus[item.orderStatus] || "暂无" }}
            </p>
          </div>
          <div class="flex justify-between items-center mt-8">
            <p class="text-[#a0a0a0] w-72 mr-10">下单时间:</p>
            <p class="text-[#565656] flex-1 flex justify-end">
              {{
                item.createTime
                  ? dateFormat(item.createTime, "yyyy-MM-dd HH:mm:ss")
                  : "暂无"
              }}
            </p>
          </div>
        </div>
      </div>
    </div>
    <div v-else class="flex justify-center items-center h-full">暂无订单</div>
  </div>
</template>

<script setup lang="ts">
import { inject, ref, watch } from "vue";
import { getImOrderList, ImOrderList, orderStatus } from "@/api/userOrder";
import { useSessionStore } from "@/store/index";
import { storeToRefs } from "pinia";
import { makeToast } from "@/utils/dom";
import { dateFormat } from "@/utils/format";
const { session } = storeToRefs(useSessionStore());
const terminal = inject("terminal");

const orderListRef = ref<ImOrderList[]>();

watch(() => session.value!, getOrderList, {
  immediate: true,
});

async function getOrderList() {
  if (session.value?.customerMucangId) {
    const res = await getImOrderList(session.value.customerMucangId);
    console.log(res.itemList);
    orderListRef.value = res.itemList;
  } else {
    makeToast("没有木仓id");
  }
}
</script>

<style scoped lang="less"></style>
