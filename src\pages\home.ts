import { EventEmitter, useEventEmitter } from "@/utils/eventEmitter";
import { computed, inject, onMounted, reactive, ref } from "vue";
import {
  Session,
  getGroupMembers,
  getSessionDetail,
  listenMsg,
} from "@/api/chat";
import { getUserInfo } from "@/api/login";
import { useSessionStore, useSseEventEmitterStore } from "@/store/index";
import { storeToRefs } from "pinia";
export function useHome(goSessionDetailsCallback?: () => void) {
  const userInfo = reactive({
    username: "",
    nickname: "",
  });
  const userAvatar = computed(() => {
    return `https://sun.mucang.cn/api/open/employee/avatar.htm?uuid=${userInfo.username}`;
  });

  const { setAllSession, setSingleSession } = useSessionStore();
  const { session } = storeToRefs(useSessionStore());
  const { setSseEventEmitter } = useSseEventEmitterStore();

  const sseEventEmitter = ref<EventEmitter>();
  const onSelectSession = async (s?: Session) => {
    const changed = session.value !== s;
    // session.value = s;
    setAllSession(s);
    if (changed && session.value) {
      // 进入会话先刷新会话信息
      const newS = await getSessionDetail({
        sessionKey: session.value.sessionKey,
      });

      session.value.groupBulletin = JSON.parse(
        newS.groupBulletin || "{}",
      ).content;
      setSingleSession("customerMucangId", newS.customerMucangId);
      setSingleSession("customerCode", newS.customerCode);
      refreshGroupMembers(session.value);
    }
    goSessionDetailsCallback && goSessionDetailsCallback();
  };

  onMounted(async () => {
    setupSSE();
    const res = await getUserInfo();
    Object.assign(userInfo, res);
  });

  const refreshGroupMembers = async (session: Session) => {
    if (session.isGroup) {
      const { itemList: memberList } = await getGroupMembers({
        sessionKey: session.sessionKey,
      });
      session.groupMembers = memberList;
      session.isDismissed = !memberList || !memberList.length;
    } else {
      session.groupMembers = undefined;
      session.isDismissed = false;
    }
  };
  const emitter = inject<EventEmitter>("emitter")!;
  // emitter.on("refreshGroupMembers", refreshGroupMembers);
  useEventEmitter(emitter, "refreshGroupMembers", refreshGroupMembers);

  const setupSSE = () => {
    const sse = (sseEventEmitter.value = new EventEmitter());
    setSseEventEmitter(sse);
    let retryDelay = 1000;
    let retryCount = 0;
    const connect = () => {
      retryCount++;
      if (retryCount > 6) {
        console.log("retry too many times, giving up");
        return;
      }
      setTimeout(() => {
        listenMsg(
          {},
          (msg) => {
            if (msg.msgType === 202) {
              setSingleSession("groupBulletin", msg.msgContent.content);
            }
            sse.emit("message", msg);
          },
          () => {
            retryDelay *= 2;
            console.log("reconnecting...");
            connect();
          },
        ).then(() => {
          retryDelay = 1000;
          retryCount = 0;
        });
      }, retryDelay);
    };
    connect();
  };

  return {
    userInfo,
    userAvatar,
    sseEventEmitter,
    session,
    onSelectSession,
  };
}
