<!-- 自定义下拉选择组件 -->
<template>
  <div
    class="custom-select relative"
    :class="{ 'is-disabled': disabled }"
    ref="selectRef"
  >
    <!-- 选择框触发器 -->
    <div
      class="select-trigger w-full px-12 py-8 border border-gray-300 rounded-lg cursor-pointer flex justify-between items-center"
      :class="{
        'bg-gray-100 cursor-not-allowed': disabled,
        'hover:border-[#04a5ff]': !disabled,
        'border-[#04a5ff]': isOpen,
      }"
      @click="toggleDropdown"
    >
      <div class="flex-1 truncate">
        {{ selectedLabel || placeholder }}
      </div>
      <div
        class="w-20 h-20 flex items-center justify-center transition-transform"
        :class="{ 'rotate-180': isOpen }"
      >
        <svg viewBox="0 0 24 24" class="w-16 h-16 fill-current">
          <path d="M7 10l5 5 5-5z" />
        </svg>
      </div>
    </div>

    <!-- 使用 Teleport 将下拉框渲染到 body -->
    <Teleport to="body">
      <div
        v-show="isOpen"
        ref="dropdownRef"
        class="select-dropdown fixed bg-white border border-gray-200 rounded-lg shadow-lg z-1 overflow-y-auto"
        :style="dropdownStyle"
      >
        <div
          v-for="option in options"
          :key="option.value"
          class="select-option px-12 py-8 cursor-pointer hover:bg-gray-50"
          :class="{ 'bg-blue-50': modelValue === option.value }"
          @click="selectOption(option)"
        >
          <slot name="option" :option="option">
            {{ option.label }}
          </slot>
        </div>
      </div>
    </Teleport>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from "vue";

interface Option {
  label: string;
  value: string | number;
  [key: string]: any;
}

const props = defineProps<{
  modelValue: string | number;
  options: Option[];
  placeholder?: string;
  disabled?: boolean;
}>();

const emit = defineEmits<{
  (e: "update:modelValue", value: string | number): void;
}>();

const isOpen = ref(false);
const selectedLabel = computed(() => {
  const option = props.options.find((opt) => opt.value === props.modelValue);
  return option?.label || "";
});

const selectRef = ref<HTMLElement | null>(null);
const dropdownRef = ref<HTMLElement | null>(null);
const dropdownStyle = ref({
  top: "0px",
  left: "0px",
  width: "0px",
  maxHeight: "300px",
});

// 切换下拉框显示状态
const toggleDropdown = () => {
  if (!props.disabled) {
    isOpen.value = !isOpen.value;
  }
};

// 选择选项
const selectOption = (option: Option) => {
  emit("update:modelValue", option.value);
  isOpen.value = false;
};

// 点击外部关闭下拉框
const handleClickOutside = (event: MouseEvent) => {
  const target = event.target as HTMLElement;
  if (!target.closest(".custom-select")) {
    isOpen.value = false;
  }
};

// 计算下拉框位置
const updateDropdownPosition = () => {
  if (!selectRef.value || !isOpen.value) return;

  const selectRect = selectRef.value.getBoundingClientRect();
  const viewportHeight = window.innerHeight;
  const viewportWidth = window.innerWidth;

  // 计算可用空间
  const spaceBelow = viewportHeight - selectRect.bottom;
  const spaceAbove = selectRect.top;
  const maxDropdownHeight = 300; // 下拉框最大高度

  // 决定显示位置和最大高度
  let showAbove = false;
  let maxHeight = maxDropdownHeight;

  if (spaceBelow < maxDropdownHeight && spaceAbove > spaceBelow) {
    // 如果下方空间不足，且上方空间更大，则显示在上方
    showAbove = true;
    maxHeight = Math.min(maxDropdownHeight, spaceAbove - 10); // 留出10px的边距
  } else {
    // 显示在下方，但需要限制高度
    maxHeight = Math.min(maxDropdownHeight, spaceBelow - 10); // 留出10px的边距
  }

  // 确保下拉框不会超出视口左右边界
  let left = selectRect.left;
  if (left + selectRect.width > viewportWidth) {
    left = viewportWidth - selectRect.width;
  }
  if (left < 0) {
    left = 0;
  }

  const top = showAbove
    ? `${selectRect.top - maxHeight}px`
    : `${selectRect.bottom}px`;

  dropdownStyle.value = {
    top,
    left: `${left}px`,
    width: `${selectRect.width}px`,
    maxHeight: `${maxHeight}px`,
  };
};

// 监听滚动事件
const handleScroll = () => {
  if (isOpen.value) {
    updateDropdownPosition();
  }
};

// 监听窗口大小变化
const handleResize = () => {
  if (isOpen.value) {
    updateDropdownPosition();
  }
};

// 监听下拉框开关状态
watch(isOpen, (newValue) => {
  if (newValue) {
    updateDropdownPosition();
    window.addEventListener("scroll", handleScroll, true);
    window.addEventListener("resize", handleResize);
  } else {
    window.removeEventListener("scroll", handleScroll, true);
    window.removeEventListener("resize", handleResize);
  }
});

onMounted(() => {
  document.addEventListener("click", handleClickOutside);
});

onUnmounted(() => {
  document.removeEventListener("click", handleClickOutside);
  window.removeEventListener("scroll", handleScroll, true);
  window.removeEventListener("resize", handleResize);
});
</script>

<style scoped>
.custom-select {
  user-select: none;
}

.select-dropdown {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e0 #f7fafc;
  transition: all 0.2s ease-in-out;
  will-change: transform, opacity;
}

.select-dropdown::-webkit-scrollbar {
  width: 6px;
}

.select-dropdown::-webkit-scrollbar-track {
  background: #f7fafc;
}

.select-dropdown::-webkit-scrollbar-thumb {
  background-color: #cbd5e0;
  border-radius: 3px;
}
</style>
