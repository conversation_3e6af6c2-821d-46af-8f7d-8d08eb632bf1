<template>
  <Model v-model="visible" bg="rgba(0,0,0,.3)" noClose>
    <div
      class="relative bg-white overflow-hidden w-500 max-w-[90vw] rounded-lg text-16"
    >
      <!-- Header with title and close button -->
      <div
        class="flex justify-between items-center border-b border-gray-200 p-20"
      >
        <div class="font-bold text-18">拼团</div>
        <div
          class="cursor-pointer absolute right-20 top-20"
          @click="$emit('close')"
        >
          <div class="w-24 h-24" v-html="iconClose"></div>
        </div>
      </div>

      <!-- Content -->
      <div class="p-20">
        <div class="mb-16">
          <label class="block text-14 text-gray-700 mb-8">拼团方式：</label>
          <select
            v-model="type"
            class="w-full px-12 py-8 border border-gray-300 rounded-lg focus:outline-none focus:border-[#04a5ff]"
          >
            <option :value="1">系统发起拼团</option>
            <option :value="2">参与进行中的拼团</option>
          </select>
        </div>
        <div class="mb-16">
          <label class="block text-14 text-gray-700 mb-8"
            >选择进行中的拼团：</label
          >
          <CustomSelect
            v-model="teamCode"
            :options="
              teamList.map((team) => ({
                label: `${team.launchUserNickName}发起的拼团（${team.joinedCount}/${team.teamSize}）`,
                value: team.teamCode,
                ...team,
              }))
            "
            :disabled="type === 1"
            placeholder="请选择"
          >
            <template #option="{ option }">
              <div class="flex flex-col">
                <div class="text-14">
                  {{ option.launchUserNickName }}发起的拼团
                </div>
                <div class="text-12 text-gray-500">
                  当前进度：（{{ option.joinedCount }}/{{ option.teamSize }}）
                </div>
                <div class="text-12 text-gray-500">
                  过期时间：{{
                    dateFormat(option.expireTime, "yyyy-MM-dd HH:mm:ss")
                  }}
                </div>
              </div>
            </template>
          </CustomSelect>
        </div>
      </div>

      <!-- Footer -->
      <div class="flex justify-end border-t border-gray-200 p-20 space-x-10">
        <button
          @click="$emit('close')"
          class="px-15 py-8 text-14 rounded-lg bg-white border border-gray-300 text-gray-700 hover:bg-gray-100"
        >
          取消
        </button>
        <button
          @click="onSend"
          :disabled="loading"
          class="px-15 py-8 text-14 rounded-lg bg-[#04a5ff] text-white hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <span v-if="loading">发送中...</span>
          <span v-else>发送拼团链接</span>
        </button>
      </div>
    </div>
  </Model>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import {
  queryOnGoingTeamList,
  sendPurchaseTeamMsg,
  MonaTeam,
} from "@/api/jxbm";
import { makeToast } from "@/utils/dom";
import Model from "@/components/Model.vue";
import CustomSelect from "@/components/Common/CustomSelect.vue";
import iconClose from "@/assets/close.svg?raw";
import { dateFormat } from "@/utils/format";

const props = defineProps<{
  templateCode: string;
  sessionKey: string;
}>();
const emits = defineEmits(["close", "success"]);

const visible = ref(true);
const type = ref<1 | 2>(1);
const teamList = ref<MonaTeam[]>([]);
const teamCode = ref("");
const loading = ref(false);

// 获取拼团列表
const fetchTeamList = async () => {
  if (!props.templateCode) return;
  loading.value = true;
  try {
    const res = await queryOnGoingTeamList({
      templateCode: props.templateCode,
    });
    teamList.value = res.itemList || [];
  } catch (error) {
    makeToast("获取拼团列表失败");
  } finally {
    loading.value = false;
  }
};

// 组件挂载时获取列表
onMounted(() => {
  fetchTeamList();
});

const onSend = async () => {
  if (type.value === 2 && !teamCode.value) {
    makeToast("请选择进行中的拼团");
    return;
  }

  loading.value = true;

  try {
    await sendPurchaseTeamMsg({
      sessionKey: props.sessionKey,
      type: type.value,
      templateCode: props.templateCode,
      teamCode: type.value === 2 ? teamCode.value : undefined,
    });
  } finally {
    loading.value = false;
  }
  emits("success");
};
</script>

<style scoped>
select option {
  padding: 8px;
  white-space: pre-wrap;
  line-height: 1.5;
}
</style>
