import { Store } from "@simplex/simple-base-sso";
import { makeToast } from "./dom";
import { URLParams, isTest } from "./tools";

export const HostNames = isTest
  ? {
      chat: "https://chat-platform.ttt.mucang.cn/",
      mona: "https://mona.ttt.mucang.cn/",
      parrot: "https://parrot-admin.ttt.mucang.cn/",
      malibu: "https://malibu.ttt.mucang.cn",
    }
  : {
      chat: "https://chat-platform.mucang.cn/",
      mona: "https://mona.kakamobi.cn/",
      parrot: "https://parrot-admin.kakamobi.cn/",
      malibu: "https://malibu.kakamobi.cn",
    };

interface RequestOptions {
  hostName?: keyof typeof HostNames; // 请求域名
  url: string;
  method?: "GET" | "POST";
  headers?: Record<string, any>;
  data?: Record<string, any>;
}

export default async function request<T>({
  hostName = "chat",
  url,
  method = "GET",
  headers,
  data = {},
  ...extra
}: RequestOptions) {
  if (hostName === "chat") {
    url += `?bizCode=${URLParams.get("bizCode") || "10001"}`;
  }

  return new Promise<T>((resolve, reject) => {
    Store.extend({
      apiCors: false,
      // 是否启动接口提示未登录时，自动登录 sso。默认不开启
      autoLogin: true,
      url: `${HostNames[hostName]}${url}`,
      headers,
      method: method.toLocaleUpperCase(),
      errorToast: false,
      type: "online",
      ...extra,
    })
      .create()
      .request(data)
      .then((res: any) => {
        resolve(res);
      })
      .fail((_errorCode: any, error: any) => {
        reject(error);
        makeToast(error.statusText || "服务器错误");
      });
  });
}
