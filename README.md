# 木仓聊天室

## 项目简介

木仓聊天室是一个统一的私教业务聊天平台，由AI加持，配备定制化的聊天回复功能。

目前主要为以下群体服务：

1. 私教教师招聘人员(面向老师，老师用企业微信客户端)
2. 私教销售人员(面向学员, 学员用驾考宝典客户端)
3. TODO: 私教教师

## 产品形态

1. PC Web 版 (在公司用电脑)
2. 移动 h5 版 (工作人员下班常用)
3. TODO: 小程序内嵌h5版 (给教师用)

## 主要功能

- 登录授权
  - 公司内部人员使用管理后台auth登录(支持web、微信和钉钉内)
  - TODO: 教师小程序内登录
- 会话管理
  - 会话列表
  - 会话标签
  - 会话搜索
  - 会话置顶、免打扰
- 聊天消息管理
  - 实时消息推送(eventSource长连接)
  - 消息列表(各种消息类型，如文本、图片、表情等)
  - 消息发送(文本、图片、视频、表情等)
  - 消息操作(撤回、回复、引用等)
- 聊天回复定制
  - AI快捷回复
  - 话术库、常见问题
  - 内嵌iframe业务: 用户画像、学情分析、报价单

## 技术栈

- Vite + Typescript
- Vue3 + Vue-Router + Pinia
  - Vue-Router只在h5端使用，改造成支持页面栈并配备页面转场动画
- 样式: Tailwind.CSS
- 实时通信: EventSource
  - 为了支持鉴权传请求头，自己用fetch封装了一个EventSource

## 核心技术组件

- VirtualList: 虚拟列表，用于会话列表的渲染，只渲染可视区域内的会话
- ContextMenu: 右键上下文菜单，用于会话置顶、免打扰等操作
- AtContainer: @提及组件，用于在输入框中@提及用户
- XScroll: 横向滚动组件，与纵向滚动的交互一致，用于标签列表的横向滚动
- RouterView: 路由视图组件，用于h5端的页面路由，支持页面栈和转场动画

## 核心业务组件

- Sessions: 会话列表，支持上拉加载更多
- MsgList: 消息列表，支持下拉加载历史消息
- MsgItem: 统一的各种类型消息的内容渲染

## 核心数据模型

- Session: 会话
- Account: 账号，对外显示的聊天账号
- Msg: 消息体
- sseEventEmitter: 聊天消息推送监听器
- bizCode: 业务码，用于区分不同的业务场景

## 其他技术细节

- svg图标复用，支持指定icon颜色
- popup组件，支持点击外部区域关闭自身
- 生成视频的封面图
- sse重连机制
