<!-- 学车地图弹窗 - 根据终端类型选择不同组件 -->
<template>
  <Win v-if="!terminal" ref="componentRef" />
  <App v-else ref="componentRef" />
</template>

<script setup lang="ts">
import { inject, ref } from "vue";
import Win from "./Win.vue";
import App from "./App.vue";

const terminal = inject("terminal", 0);
const componentRef = ref<{
  open: (
    sessionKey: string,
    latitude?: number,
    longitude?: number,
    userId?: string,
  ) => void;
}>();

defineExpose({
  open: (
    sessionKey: string,
    latitude?: number,
    longitude?: number,
    userId?: string,
  ) => {
    componentRef.value?.open(sessionKey, latitude, longitude, userId);
  },
});
</script>
