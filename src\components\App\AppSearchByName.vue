<!-- App中的搜索 -->
<template>
  <div class="app-search-name-container mx-auto relative flex flex-col h-full">
    <div class="w-full bg-[#e6f2fb] px-[0.3rem] pt-[.16rem] h-[1.84rem]">
      <div
        class="flex bg-[#f9f9f9] text-[0.28rem] items-center h-[0.68rem] rounded-[0.16rem]"
      >
        <div
          class="w-[0.32rem] h-[0.32rem] ml-[0.3rem] bg-cover block bg-[url(./assets/top_search.png)]"
        ></div>
        <input
          type="text"
          name=""
          id=""
          placeholder="搜索"
          class="px-[0.24rem] bg-transparent placeholder:text-[#abafb1] outline-none w-full"
          ref="inputDomRef"
          @input="onSearchValueInput"
        />
      </div>
      <div class="flex pt-[0.4rem]">
        <div
          class="text-[0.3rem] px-[0.2rem] pb-[0.15rem] ml-[.55rem] category"
          v-for="item in categoryArr"
          :key="item.id"
          :class="{
            'text-[#04a5ff] border-b-[0.04rem] border-solid border-[#04a5ff]':
              categoryTypeRef === item.id,
            'text-[#333333]': categoryTypeRef !== item.id,
          }"
          @click="onSwitchCategoryClick(item.id)"
        >
          {{ item.label }}
        </div>
      </div>
    </div>
    <div class="flex-1 h-0 flex flex-col overflow-auto">
      <div class="px-[0.3rem]">
        <template v-for="item in ListSessionResRef">
          <div v-if="categoryTypeRef === 1 || item.id === categoryTypeRef">
            <SearchSessionList
              :sessionList="item"
              @view="onSwitchCategoryClick"
              @goSession="onGoSession"
            ></SearchSessionList>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { inject, onMounted, ref } from "vue";
import { debounce } from "lodash";
import { listSessions, ListSessionRes } from "@/api/chat";
import { Category, SearchListSession } from "./type";
import SearchSessionList from "./SearchSessionList.vue";
import { EventEmitter } from "@/utils/eventEmitter";

const emitter = inject<EventEmitter>("emitter")!;
const inputDomRef = ref<HTMLInputElement>();
const ListSessionResRef = ref<SearchListSession[]>([]);
let recordTabId: Category["id"] = 1;

const categoryArr: Category[] = [
  {
    id: 1,
    label: "全部",
  },
  {
    id: 2,
    label: "单聊",
  },
  {
    id: 3,
    label: "群聊",
  },
];

const categoryTypeRef = ref<Category["id"]>(categoryArr[0].id);

const onSearchValueInput = debounce(async (e: Event) => {
  const target = e.target as HTMLInputElement;
  ListSessionResRef.value = [];
  if (!target.value) {
    return;
  }
  const res = await listSessions({ name: target.value });
  const reg = new RegExp(target.value, "ig");
  for (const key in res) {
    const item = res[key as keyof ListSessionRes];
    ListSessionResRef.value?.push({
      id: key === "singleChatList" ? 2 : 3,
      label: key === "singleChatList" ? "单聊" : "群聊",
      list: item.map((it) => ({
        ...it,
        name: it.name.replace(reg, `<span class="text-[#04a5ff]">$&</span>`),
      })),
      showViewBut: recordTabId === 1,
    });
  }
}, 500);

const onSwitchCategoryClick = (id: Category["id"]) => {
  recordTabId = id;
  if (id === 1) {
    ListSessionResRef.value = ListSessionResRef.value.map((item) => ({
      ...item,
      showViewBut: true,
    }));
  } else {
    ListSessionResRef.value = ListSessionResRef.value.map((item) => ({
      ...item,
      showViewBut: false,
    }));
  }
  categoryTypeRef.value = id;
};
const onGoSession = (key: string) => {
  emitter.emit("goSession", key);
  reset();
};

const reset = () => {
  ListSessionResRef.value = [];
};

onMounted(() => {
  // 等待页面转场动画执行完
  setTimeout(() => {
    inputDomRef.value?.focus();
  }, 300);
});
</script>

<style scoped>
.category:first-child {
  margin-left: 0;
}
</style>
