<!-- 学车地图页面 - 移动版 -->
<template>
  <!-- 引入学车地图组件 -->
  <AMapPopUpApp ref="mapComponentRef" />
</template>

<script setup lang="ts">
import { onMounted, ref } from "vue";
import { useRoute } from "vue-router";
import AMapPopUpApp from "@/components/Common/AMapPopUp/App.vue";

const route = useRoute();
const mapComponentRef = ref();

// 获取路由参数
const sessionKey = route.query.sessionKey as string;
const latitude = route.query.latitude
  ? Number(route.query.latitude)
  : undefined;
const longitude = route.query.longitude
  ? Number(route.query.longitude)
  : undefined;
const userId = route.query.userId as string;
onMounted(() => {
  // 页面加载后初始化地图组件
  if (sessionKey) {
    // 这里不需要调用 open 方法，因为我们已经在地图页面了
    // 直接使用组件内部的方法初始化地图
    mapComponentRef.value?.initMap?.(sessionKey, latitude, longitude, userId);
  }
});
</script>
