<template>
  <div class="w-screen h-screen">
    <div
      class="flex items-center hover:bg-[#e6f2fb] hover:cursor-pointer py-10 box-border rounded-4 px-8"
      :class="{
        'bg-[#e6f2fb] text-[#04a5ff]': item.id === account?.id,
      }"
      v-for="item in accountList"
      :key="item.id"
      @click="selectAccount(item)"
    >
      <img
        class="w-[.8rem] h-[.8rem] rounded-full object-cover"
        style="max-width: unset"
        :src="item.avatar"
      />
      <div
        class="ml-8 overflow-hidden overflow-ellipsis whitespace-nowrap text-[0.3rem]"
        :title="item.name"
      >
        {{ item.name }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useAccoutSelect } from "./index";

const { account, accountList, selectAccount } = useAccoutSelect();
</script>

<style lang="less" scoped>
.left-area {
  .avatar-area {
    .avatar {
      width: 0.7rem;
      height: 0.7rem;
      border-radius: 50%;
    }

    .select-icon {
      margin-left: 2px;
      width: 0.2rem;
      height: 0.2rem;
    }
  }

  .select-list {
    position: absolute;
    top: -0.2rem;
    left: 0.99rem;

    .content {
      display: flex;
      align-items: center;
      border-radius: 5px;
      font-size: 0.3rem;
      padding: 5px 5px;

      .avatar {
        width: 0.4rem;
        height: 0.4rem;
      }

      .text {
        margin-left: 6px;
        max-width: 90px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-size: 0.3rem;
      }
    }
  }
}
</style>
