/**
 * 因为vue-router很多逻辑写死了，它的组件在不同路由之间可以复用，与我们基于路由栈的设计相违背
 * 这里主要是要改route.matched.instances
 */

import * as vue from "vue";
import {
  matchedRouteKey,
  routerViewLocationKey,
  viewDepthKey,
} from "vue-router";

const assign = Object.assign;
const isBrowser = typeof window !== "undefined";
const isArray = Array.isArray;

function isSameRouteRecord(a, b) {
  // since the original record has an undefined value for aliasOf
  // but all aliases point to the original record, this will always compare
  // the original record
  return (a.aliasOf || a) === (b.aliasOf || b);
}

function normalizeSlot(slot, data) {
  if (!slot) return null;
  const slotContent = slot(data);
  return slotContent.length === 1 ? slotContent[0] : slotContent;
}

export const RouterView = /*#__PURE__*/ vue.defineComponent({
  name: "RouterView",
  // #674 we manually inherit them
  inheritAttrs: false,
  props: {
    name: {
      type: String,
      default: "default",
    },
    route: Object,
  },
  // Better compat for @vue/compat users
  // https://github.com/vuejs/router/issues/1315
  compatConfig: { MODE: 3 },
  setup(props, { attrs, slots }) {
    const injectedRoute = vue.inject(routerViewLocationKey);
    const routeToDisplay = vue.computed(
      () => props.route || injectedRoute.value,
    );
    const injectedDepth = vue.inject(viewDepthKey, 0);
    // The depth changes based on empty components option, which allows passthrough routes e.g. routes with children
    // that are used to reuse the `path` property
    const depth = vue.computed(() => {
      let initialDepth = vue.unref(injectedDepth);
      const { matched } = routeToDisplay.value;
      let matchedRoute;
      while (
        (matchedRoute = matched[initialDepth]) &&
        !matchedRoute.components
      ) {
        initialDepth++;
      }
      return initialDepth;
    });
    const matchedRouteRef = vue.computed(
      () => routeToDisplay.value.matched[depth.value],
    );
    vue.provide(
      viewDepthKey,
      vue.computed(() => depth.value + 1),
    );
    vue.provide(matchedRouteKey, matchedRouteRef);
    vue.provide(routerViewLocationKey, routeToDisplay);
    const viewRef = vue.ref();
    // watch at the same time the component instance, the route record we are
    // rendering, and the name
    vue.watch(
      () => [viewRef.value, matchedRouteRef.value, props.name],
      ([instance, to, name], [oldInstance, from, oldName]) => {
        // copy reused instances
        if (to) {
          // this will update the instance for new instances as well as reused
          // instances when navigating to a new route
          to.instances[name] = instance;

          // 这里就是需要魔改的地方
          to.instances[routeToDisplay.value.fullPath] = instance;

          // the component instance is reused for a different route or name, so
          // we copy any saved update or leave guards. With async setup, the
          // mounting component will mount before the matchedRoute changes,
          // making instance === oldInstance, so we check if guards have been
          // added before. This works because we remove guards when
          // unmounting/deactivating components
          if (from && from !== to && instance && instance === oldInstance) {
            if (!to.leaveGuards.size) {
              to.leaveGuards = from.leaveGuards;
            }
            if (!to.updateGuards.size) {
              to.updateGuards = from.updateGuards;
            }
          }
        }
        // trigger beforeRouteEnter next callbacks
        if (
          instance &&
          to &&
          // if there is no instance but to and from are the same this might be
          // the first visit
          (!from || !isSameRouteRecord(to, from) || !oldInstance)
        ) {
          (to.enterCallbacks[name] || []).forEach((callback) =>
            callback(instance),
          );
        }
      },
      { flush: "post" },
    );
    return () => {
      const route = routeToDisplay.value;
      // we need the value at the time we render because when we unmount, we
      // navigated to a different location so the value is different
      const currentName = props.name;
      const matchedRoute = matchedRouteRef.value;
      const ViewComponent =
        matchedRoute && matchedRoute.components[currentName];
      if (!ViewComponent) {
        return normalizeSlot(slots.default, {
          Component: ViewComponent,
          route,
        });
      }
      // props from route configuration
      const routePropsOption = matchedRoute.props[currentName];
      const routeProps = routePropsOption
        ? routePropsOption === true
          ? route.params
          : typeof routePropsOption === "function"
            ? routePropsOption(route)
            : routePropsOption
        : null;
      const onVnodeUnmounted = (vnode) => {
        // remove the instance reference to prevent leak
        if (vnode.component.isUnmounted) {
          matchedRoute.instances[currentName] = null;

          // 这里就是需要魔改的地方
          matchedRoute.instances[route.fullPath] = null;
        }
      };
      const component = vue.h(
        ViewComponent,
        assign({}, routeProps, attrs, {
          onVnodeUnmounted,
          ref: viewRef,
        }),
      );
      if (isBrowser && component.ref) {
        // TODO: can display if it's an alias, its props
        const info = {
          depth: depth.value,
          name: matchedRoute.name,
          path: matchedRoute.path,
          meta: matchedRoute.meta,
        };
        const internalInstances = isArray(component.ref)
          ? component.ref.map((r) => r.i)
          : [component.ref.i];
        internalInstances.forEach((instance) => {
          // @ts-expect-error
          instance.__vrv_devtools = info;
        });
      }
      return (
        // pass the vnode to the slot as a prop.
        // h and <component :is="..."> both accept vnodes
        normalizeSlot(slots.default, { Component: component, route }) ||
        component
      );
    };
  },
});
