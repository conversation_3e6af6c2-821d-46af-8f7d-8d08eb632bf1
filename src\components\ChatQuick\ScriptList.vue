<!-- 脚本列表 -->
<template>
  <div class="flex flex-col mt-14 h-full">
    <div class="flex-1">
      <div
        class="px-14 py-8 rounded-5 mb-10"
        :style="{
          backgroundColor: props.bgFn(item) || '#f8fcff',
        }"
        v-for="item in dataList"
      >
        <div class="flex items-center text-14 gap-x-11">
          <div class="flex-1 text-[#333333]">
            <slot name="title" :item="item"></slot>
          </div>
          <div
            class="w-23 h-23 text-center cursor-pointer bg-[url(@/assets/ic_fs.png)] bg-cover hover:bg-[url(@/assets/ic_fs_hover.png)]"
            @click="emit('send', item)"
          ></div>
          <div
            class="text-center w-23 h-23 [transition:.3s_all]"
            :class="{
              'rotate-[180deg]': detailsKeyRef === headleDetailsKey(item),
            }"
          >
            <img
              src="@/assets/ic_down.png"
              alt=""
              v-if="disable ? disable(item) : true"
              @click="onPreviewClick(item)"
              class="cursor-pointer"
            />
          </div>
        </div>
        <div class="" v-if="detailsKeyRef === headleDetailsKey(item)">
          <slot :item="item"></slot>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { InstructionList } from "@/api/instruct";
import { ref } from "vue";
import { QAItem, QuickMsgActionType } from "@/api/chat";

import {
  ScriptListType,
  isInstructionList,
  isQAItem,
  isQuickMsgActionType,
} from "./Common/type";

const emit = defineEmits<{
  (e: "send", instructionKey: ScriptListType): void;
}>();

const props = withDefaults(
  defineProps<{
    dataList:
      | InstructionList["itemList"]
      | QuickMsgActionType[]
      | (QAItem & { id: number })[];
    disable?: (item: ScriptListType) => boolean;
    onPreviewFn?: (item: ScriptListType) => void;
    bgFn?(item: ScriptListType): string | undefined;
  }>(),
  {
    bgFn: () => "#f8fcff",
  },
);

const detailsKeyRef = ref<string | number>("");

const onPreviewClick = (item: ScriptListType) => {
  props.onPreviewFn && props.onPreviewFn(item);
  detailsKeyRef.value =
    detailsKeyRef.value === headleDetailsKey(item)
      ? ""
      : headleDetailsKey(item);
};

const headleDetailsKey = (item: ScriptListType): string | number => {
  if (isInstructionList(item)) {
    return item.instructionKey;
  } else if (isQuickMsgActionType(item) || isQAItem(item)) {
    return item.id;
  } else {
    return 1;
  }
};
</script>

<style scoped lang="less"></style>
