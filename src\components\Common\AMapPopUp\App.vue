<!-- 学车地图组件 - 移动版 -->
<template>
  <div class="h-[100vh] flex flex-col bg-[#fff]" v-if="visibleRef">
    <!-- 内容区域 -->
    <div class="flex-1 flex flex-col overflow-hidden">
      <div class="w-full px-[.4rem] pt-[.4rem]">
        <div class="h-[1.4rem] flex items-center relative">
          <label
            class="w-full border-1 border-solid border-[#e5e5e5] rounded-5 flex items-center px-[.2rem] h-[.7rem]"
          >
            <input
              id="search-input-mobile"
              type="text"
              placeholder="输入学员所在地以搜索"
              class="outline-none flex-1 pr-[.2rem] text-[.3rem]"
            />
            <div
              class="w-[.3rem] h-[.3rem] bg-cover block bg-[url(./assets/top_search.png)]"
            ></div>
          </label>
          <div
            id="search-result-mobile"
            class="absolute bg-[#fff] w-full z-[9999] left-0 top-[1.2rem] border-1 border-solid border-[#ccc] rounded-5 max-h-[50vh] overflow-y-auto"
          ></div>
        </div>

        <div
          id="amap-container"
          class="h-[70vh] border-1 border-solid border-[#ccc] mobile-map"
        ></div>
      </div>

      <!-- 训练场信息弹窗 -->
      <div
        v-if="trainingGroundInfoRef"
        class="fixed inset-0 bg-black bg-opacity-50 z-[9999999] flex items-center justify-center"
        @click.self="closeTrainingInfo"
      >
        <div
          class="bg-[#F2FAFF] w-[90vw] max-h-[70vh] rounded-[.3rem] overflow-hidden"
        >
          <!-- 弹窗头部 -->
          <div
            class="flex justify-between items-center px-[.4rem] py-[.3rem] border-b-1 border-solid border-[#e5e5e5]"
          >
            <div class="text-[.32rem] font-bold">
              {{ trainingGroundInfoRef?.trainingFieldName }}
            </div>
            <div
              class="w-[.6rem] h-[.6rem] flex items-center justify-center"
              @click="closeTrainingInfo"
            >
              <div
                class="w-[.3rem] h-[.3rem] bg-cover bg-[url(../../../assets/close.svg)]"
              ></div>
            </div>
          </div>

          <!-- 弹窗内容区 -->
          <div class="px-[.4rem] py-[.3rem] overflow-y-auto max-h-[60vh]">
            <div class="pb-[.2rem]">
              <table class="w-full border-collapse text-[.3rem]">
                <tbody>
                  <tr>
                    <td
                      class="text-[#7F7F7F] w-[2.2rem] align-top py-[.1rem] pr-[.2rem]"
                    >
                      所属驾校：
                    </td>
                    <td class="text-[#333] align-top py-[.1rem]">
                      {{ trainingGroundInfoRef?.jiaxiaoName }}
                    </td>
                  </tr>
                  <tr>
                    <td
                      class="text-[#7F7F7F] w-[2.2rem] align-top py-[.1rem] pr-[.2rem]"
                    >
                      距离：
                    </td>
                    <td class="text-[#333] align-top py-[.1rem]">
                      {{ trainingGroundInfoRef?.distanceText }}
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>

            <!-- 班级列表 -->
            <div
              class="text-[.26rem] pb-[.2rem]"
              v-if="
                trainingGroundInfoRef.classList &&
                trainingGroundInfoRef.classList.length > 0
              "
            >
              <div
                class="bg-[#fff] border-1 border-solid border-[#e5e5e5] rounded-5 p-[.2rem] text-[#333] mb-[.2rem]"
                v-for="item in trainingGroundInfoRef.classList"
                :key="item.classCode"
              >
                <div>{{ item.className }}</div>
                <div class="flex mt-[.2rem]">
                  <div class="w-[1.4rem]">商品编码</div>
                  <div>{{ item.goodsCode }}</div>
                </div>
                <div class="flex mt-[.2rem]">
                  <div class="w-[1.4rem]">价格</div>
                  <div>{{ item.price }}</div>
                </div>
                <div class="h-[.62rem] mt-[.2rem] flex">
                  <div
                    class="bg-[#04a5ff] text-[#fff] rounded-5 text-center text-[.28rem] leading-[.62rem] cursor-pointer flex-1"
                    @click="sendMessageAndGoBack(item)"
                  >
                    学车商品卡片
                  </div>
                  <div class="w-[.1rem]"></div>
                  <div
                    class="flex-1 border-1 border-solid border-[#ccc] text-center text-14/31 cursor-pointer rounded-5"
                    @click="onReservationClick(item)"
                  >
                    预约表单
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <Loading :isLoading="loadingRef"></Loading>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onBeforeUnmount } from "vue";
import { useRouter } from "vue-router";
import { useAMapPopUp } from "./index";
import Loading from "@/components/Loading.vue";
import { ClassList } from "@/api/jxbm";

const router = useRouter();

const {
  trainingGroundInfoRef,
  loadingRef,
  visibleRef,
  open,
  reset,
  sendJXBMMessage,
  onReservationClick,
} = useAMapPopUp();

// 初始化地图
const initMap = (
  sessionKey: string,
  latitude?: number,
  longitude?: number,
  userId?: string,
) => {
  open(sessionKey, latitude, longitude, userId);
};

// 关闭训练场信息弹窗
const closeTrainingInfo = () => {
  // 将训练场信息设置为空，关闭弹窗
  trainingGroundInfoRef.value = undefined;
};

// 发送消息并返回上一页
const sendMessageAndGoBack = async (item: ClassList) => {
  // 先发送消息
  await sendJXBMMessage(item);
  // 然后返回上一页
  onBackClick();
};

// 对外暴露的方法
defineExpose({
  // 打开地图组件
  open: (
    sessionKey: string,
    latitude?: number,
    longitude?: number,
    userId?: string,
  ) => {
    console.log("userId", userId);

    // 跳转到学车地图页面
    router.push({
      name: "AMapPopUp",
      query: {
        sessionKey,
        latitude: latitude?.toString(),
        longitude: longitude?.toString(),
        userId,
      },
    });
  },
  // 初始化地图方法，用于页面组件调用
  initMap,
});

// 页面卸载时清理资源
onBeforeUnmount(() => {
  reset();
});

// 返回上一页
const onBackClick = () => {
  router.back();
};
</script>

<style lang="less">
/* 移动端地图样式 */
.mobile-map {
  .amap-icon-container {
    box-sizing: border-box;
    min-width: 2rem;
    max-width: 4rem;
    padding: 0.1rem 0.2rem;
    background-color: #5aa7f5;
    border-radius: 5px;
    color: #fff;
    text-align: center;
    line-height: 0.4rem;
    position: absolute;
    display: flex;
    flex-direction: column;
    align-items: center;
    transform: translateZ(0);
    /* 不再使用transform来定位，改用bottom属性 */
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    font-size: 0.26rem;
    pointer-events: auto; /* 确保点击事件正常工作 */
  }
  .amap-icon-arrow {
    border-top: 0.2rem solid #5aa7f5;
    border-left: 0.2rem solid transparent;
    border-right: 0.2rem solid transparent;
    border-bottom: 0.2rem solid transparent;
    position: absolute;
    bottom: -0.4rem;
    left: 50%;
    margin-left: -0.2rem;
    z-index: 1; /* 确保箭头在正确的层级 */
  }

  .amap-icon-container.active {
    background-color: #f59a23;
    .amap-icon-arrow {
      border-top: 0.2rem solid #f59a23;
    }
  }

  .amap-marker {
    width: min-content;
    position: absolute;

    .amap-marker-content {
      width: min-content;
    }
  }
  .amap-icon img,
  .amap-marker-content img {
    width: 0.5rem;
    height: 0.68rem;
  }

  .marker {
    position: absolute;
    top: -0.4rem;
    right: -2.36rem;
    color: #fff;
    padding: 0.08rem 0.2rem;
    box-shadow: 1px 1px 1px rgba(10, 10, 10, 0.2);
    white-space: nowrap;
    font-size: 0.24rem;
    font-family: "";
    background-color: #25a5f7;
    border-radius: 3px;
  }

  .input-card {
    width: 3.6rem;
    z-index: 1;
  }

  .input-card .btn {
    margin-right: 0.16rem;
  }

  .input-card .btn:last-child {
    margin-right: 0;
  }
}
</style>
