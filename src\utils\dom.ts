import promptStyles from "./prompt.module.less";

/** 模拟toast */
export function makeToast(message: string, foz = "18px", time = 2000) {
  return new Promise((resolve) => {
    const myToast = document.getElementById("myToast");
    if (myToast) {
      document.body.removeChild(myToast);
    }

    const mask: HTMLDivElement = document.createElement("div");
    mask.style.position = "fixed";
    mask.style.left = "0";
    mask.style.right = "0";
    mask.style.top = "0";
    mask.style.bottom = "0";
    mask.style.backgroundColor = "rgba(0,0,0,0.3)";
    mask.style.zIndex = "9999999";

    const div: HTMLDivElement = document.createElement("div");
    mask.appendChild(div);
    div.innerText = message;

    mask.setAttribute("id", "myToast");

    div.style.position = "absolute";
    div.style.left = "50%";
    div.style.top = "20%";
    div.style.transform = "translate(-50%, -50%)";
    div.style.webkitTransform = "translate(-50%, -50%)";
    div.style.background = "rgba(0, 0, 0, 0.7)";
    div.style.zIndex = "9999";
    div.style.padding = "8px 18px";
    div.style.borderRadius = "6px";
    div.style.textAlign = "center";
    div.style.color = "#ffffff";
    div.style.maxWidth = "90%";
    // div.style.minWidth = '60%';
    div.style.fontSize = foz;
    div.style.lineHeight = "1.5";

    document.body.appendChild(mask);
    setTimeout(function () {
      mask.remove();
      resolve("");
    }, time);
  });
}

/* 滚动到可视区域 */
export function scrollIntoView(
  dom: HTMLElement,
  options?: {
    topThreshold?: number;
    leftThreshold?: number;
    bottomThreshold?: number;
    rightThreshold?: number;
    center?: boolean;
    scroller?: HTMLElement;
  },
) {
  const {
    topThreshold = 0,
    leftThreshold = 0,
    bottomThreshold = 0,
    rightThreshold = 0,
    center = false,
  } = options || {};

  const scroller = options?.scroller || dom.parentElement!;

  const { scrollTop = 0, scrollLeft = 0 } = scroller;
  const scrollBottom = scrollTop + scroller.clientHeight;
  const scrollRight = scrollLeft + scroller.clientWidth;

  const { offsetTop, offsetLeft } = dom;
  const offsetBottom = offsetTop + dom.offsetHeight;
  const offsetRight = offsetLeft + dom.offsetWidth;

  const scrollOptions: { top: number; left: number } = { top: 0, left: 0 };

  if (offsetTop - topThreshold < scrollTop) {
    scrollOptions.top = offsetTop - topThreshold - scrollTop;
  } else if (offsetBottom + bottomThreshold > scrollBottom) {
    scrollOptions.top = offsetBottom + bottomThreshold - scrollBottom;
  }

  if (offsetLeft - leftThreshold < scrollLeft) {
    scrollOptions.left = offsetLeft - leftThreshold - scrollLeft;
  } else if (offsetRight + rightThreshold > scrollRight) {
    scrollOptions.left = offsetRight + rightThreshold - scrollRight;
  }

  if (center) {
    scrollOptions.top =
      (offsetTop + offsetBottom) / 2 - (scrollTop + scrollBottom) / 2;
    scrollOptions.left =
      (offsetLeft + offsetRight) / 2 - (scrollLeft + scrollRight) / 2;
  }

  scrollOptions.top += scrollTop;
  scrollOptions.left += scrollLeft;

  if (scroller.scrollTo) {
    scroller.scrollTo({
      ...scrollOptions,
      behavior: "smooth",
    });
  } else {
    // 低版本没有scroll方法
    scroller.scrollTop = scrollOptions.top;
    scroller.scrollLeft = scrollOptions.left;
  }
}

interface Prompt {
  /** 标题 */
  title: string;
  /** 内容 */
  content?: string;
  /** 是否显示输入框 */
  showInputArea?: boolean;
  /** 是否显示取消按钮 */
  showCancel?: boolean;
  /** 终端 0: pc  1:app */
  terminal?: 0 | 1 | undefined;
}

/**弹窗确认框 */
export function prompt({
  title,
  content,
  showInputArea = false,
  terminal = undefined,
  showCancel = true,
}: Prompt) {
  console.log(terminal);

  return new Promise<string>((resolve, reject) => {
    const body = document.body;
    const model = document.createElement("div");
    model.className = promptStyles["prompt-container"];
    const terminalClassName = terminal === 0 ? "prompt-win" : "prompt-app";
    const showInput = `
      <div class='${promptStyles["prompt-main"]} ${promptStyles[terminalClassName]}' >
        <div class='${promptStyles["title"]}'>
          ${title}
        </div>
        <div class='${promptStyles["prompt-input"]} ${promptStyles["input"]}'>
          ${content} 
          <div>
            <input type='datetime-local' class='inputDom'/>
          </div>
        </div>
        <div class='${promptStyles["prompt-but"]} ${promptStyles["but"]}'>
          ${showCancel ? `<button class='close'>取消</button>` : ""}
          <button class='confirm'>确定</button>
        </div>
      </div>
    `;
    const noShowInput = `<div class='${promptStyles["prompt-main"]} ${promptStyles[terminalClassName]}' >
        <div class='${promptStyles["title"]}'>
          ${title}
        </div>
        <div class='${promptStyles["prompt-input"]} ${promptStyles["input"]}'> ${content || ""} </div>
        <div class='${promptStyles["prompt-but"]} ${promptStyles["but"]}'>
          ${showCancel ? `<button class='close'>取消</button>` : ""}
          <button class='confirm'>确定</button>
        </div>
      </div>`;
    const modelHtml = showInputArea ? showInput : noShowInput;
    model.innerHTML = modelHtml;
    const close = () => {
      body.removeChild(model);
      model.querySelector(".close")?.removeEventListener("click", close);
      model.querySelector(".confirm")?.removeEventListener("click", confirm);
      reject();
    };
    const confirm = () => {
      const inputDom = document.querySelector(".inputDom") as HTMLInputElement;
      body.removeChild(model);
      resolve(inputDom ? inputDom.value : "");
    };
    setTimeout(() => {
      model.querySelector(".close")?.addEventListener("click", close);
      model.querySelector(".confirm")?.addEventListener("click", confirm);
    }, 100);
    body.appendChild(model);
  });
}

interface DraggableBoxOptions {
  content: string | HTMLElement; // 支持字符串或DOM元素内容
  title?: string; // 可选标题
  initialPosition?: { x: number; y: number }; // 初始位置
  showClose?: boolean; // 是否显示关闭按钮
  showCloseFn?: () => void;
  class?: string; // 自定义class
}
/**
 * 显示提示框(可拖动)
 */
export function createDraggableBox(options: DraggableBoxOptions): {
  close: () => void;
  updateContent: (txt: string | HTMLElement) => void;
} {
  // 创建对话框容器
  const box = document.createElement("div");
  options.class && box.classList.add(options.class);
  box.style.cssText = `
      position: fixed;
      background: white;
      border: 1px solid #ccc;
      box-shadow: 0 2px 10px rgba(0,0,0,0.2);
      min-width: 300px;
      min-height: 200px;
      z-index: 9999;
      display: flex;
      flex-direction: column;
  `;

  // 设置初始位置
  if (options.initialPosition) {
    box.style.left = `${options.initialPosition.x}px`;
    box.style.top = `${options.initialPosition.y}px`;
  } else {
    // 默认居中
    box.style.left = "50%";
    box.style.top = "50%";
    box.style.transform = "translate(-50%, -50%)";
  }

  // 创建标题栏
  const header = document.createElement("div");
  header.style.cssText = `
      padding: 10px;
      background: #f5f5f5;
      border-bottom: 1px solid #ddd;
      cursor: move;
      user-select: none;
      display: flex;
      justify-content: space-between;
      align-items: center;
  `;

  // 标题文字
  const title = document.createElement("span");
  title.textContent = options.title || "Dialog";
  header.appendChild(title);

  // 关闭按钮
  if (options.showClose) {
    const closeBtn = document.createElement("button");
    closeBtn.textContent = "×";
    closeBtn.style.cssText = `
      background: none;
      border: none;
      font-size: 1.2em;
      cursor: pointer;
      padding: 0 8px;
  `;
    closeBtn.onclick = () => {
      document.body.removeChild(box);
      options.showCloseFn && options.showCloseFn();
    };
    header.appendChild(closeBtn);
  }

  // 内容区域
  const content = document.createElement("div");
  content.style.padding = "15px";
  content.style.flexGrow = "1";
  content.style.overflow = "auto";

  // 处理不同类型的内容
  if (typeof options.content === "string") {
    content.innerHTML = options.content;
  } else {
    content.appendChild(options.content);
  }
  if ("ontouchstart" in window) {
    box.style.minWidth = "20vw"; // 移动端更合适的默认宽度
    box.style.fontSize = ".5rem";
    header.style.fontSize = ".4rem";
    header.style.padding = "15px 10px";
  }
  // 组装对话框
  box.appendChild(header);
  box.appendChild(content);
  document.body.appendChild(box);

  // 拖拽功能实现
  let isDragging = false;
  let startX = 0;
  let startY = 0;
  let startLeft = 0;
  let startTop = 0;

  header.addEventListener("mousedown", startDrag);
  header.addEventListener("touchstart", startDrag, { passive: false });
  function getClientCoordinates(e: MouseEvent | TouchEvent): {
    x: number;
    y: number;
  } {
    if (e instanceof TouchEvent) {
      return {
        x: e.touches[0].clientX,
        y: e.touches[0].clientY,
      };
    }
    return {
      x: e.clientX,
      y: e.clientY,
    };
  }

  function startDrag(e: MouseEvent | TouchEvent) {
    // 阻止默认触摸行为（防止页面滚动）
    if (e instanceof TouchEvent) {
      e.preventDefault();
    }

    isDragging = true;
    const coords = getClientCoordinates(e);
    startX = coords.x;
    startY = coords.y;

    const rect = box.getBoundingClientRect();
    startLeft = rect.left;
    startTop = rect.top;

    // 添加事件监听（同时支持鼠标和触摸）
    document.addEventListener("mousemove", onDrag);
    document.addEventListener("touchmove", onDrag, { passive: false });
    document.addEventListener("mouseup", stopDrag);
    document.addEventListener("touchend", stopDrag);
  }

  function onDrag(e: MouseEvent | TouchEvent) {
    if (!isDragging) return;

    // 阻止触摸默认行为
    if (e instanceof TouchEvent) {
      e.preventDefault();
    }

    const coords = getClientCoordinates(e);
    const deltaX = coords.x - startX;
    const deltaY = coords.y - startY;

    // 边界检测逻辑（保持原有代码不变）
    let newLeft = startLeft + deltaX;
    let newTop = startTop + deltaY;

    const rect = box.getBoundingClientRect();
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    newLeft = Math.max(0, Math.min(newLeft, viewportWidth - rect.width));
    newTop = Math.max(0, Math.min(newTop, viewportHeight - rect.height));

    box.style.left = `${newLeft}px`;
    box.style.top = `${newTop}px`;
    box.style.transform = "none";
  }

  function stopDrag() {
    isDragging = false;
    // 移除所有事件监听
    document.removeEventListener("mousemove", onDrag);
    document.removeEventListener("touchmove", onDrag);
    document.removeEventListener("mouseup", stopDrag);
    document.removeEventListener("touchend", stopDrag);
  }

  return {
    close: () => {
      document.body.removeChild(box);
    },
    updateContent: (txt: string | HTMLElement) => {
      content.innerHTML = "";
      if (typeof txt === "string") {
        content.innerHTML = txt;
      } else {
        content.appendChild(txt);
      }
    },
  };
}
