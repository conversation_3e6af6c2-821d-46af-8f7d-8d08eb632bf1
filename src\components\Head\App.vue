<template>
  <div class="head flex items-center px-20 bg-[#e6f2fb] z-1">
    <div class="w-[0.5rem] h-[0.5rem]">
      <div
        class="w-full h-full bg-cover bg-[url(./assets/ic_back.png)]"
        @click="$emit('back')"
        v-if="!isBut"
      ></div>
    </div>

    <div
      class="title flex-1 text-center font-bold text-[rgba(0,0,0,0.85)] flex justify-center"
    >
      <div
        :class="
          terminal && pages === 'chat'
            ? 'whitespace-nowrap overflow-hidden overflow-ellipsis w-[3rem]'
            : ''
        "
      >
        {{ title || "木仓聊天室" }}
      </div>
      <div
        v-if="terminal && pages === 'home'"
        class="svg bg-cover bg-[url(./assets/top_Refresh.png)]"
        @click="$emit('refresh')"
      ></div>
    </div>
    <div class="flex items-center w-[0.5rem] h-[0.5rem]">
      <!-- <div class="text text-[#333333]">
        {{ userInfo.nickname }}
      </div>
      <img class="avatar" :src="userAvatar" /> -->
    </div>
  </div>
</template>

<script lang="ts" setup>
import { inject } from "vue";

defineEmits(["refresh", "back"]);
defineProps<{
  userInfo: {
    username: string;
    nickname: string;
  };
  userAvatar: string;
  isBut?: boolean;
  title?: string;
  pages: "home" | "chat";
}>();

const terminal = inject<number>("terminal");
</script>

<style lang="less" scoped>
.head {
  height: 0.8rem;
  .title {
    font-size: 0.34rem;
  }
  .svg {
    width: 0.48rem;
    height: 0.48rem;
    margin-left: 5px;
  }
}
</style>
