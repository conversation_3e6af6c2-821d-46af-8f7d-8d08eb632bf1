<template>
  <div :class="{ msgItemContainer: !!terminal }">
    <div v-if="msg.msgType === 11">
      <span
        class="content"
        v-html="processEmojiText(processAtUsers(msg as Msg))"
      ></span>
    </div>
    <div v-else-if="msg.msgType === 10006">
      <img
        :src="singleEmojiMapper[msg.msgContent.text]"
        alt=""
        v-if="singleEmojiMapper[msg.msgContent.text]"
      />
      <span class="content" v-else>{{ msg.msgContent.text }}</span>
    </div>
    <div v-else-if="msg.msgType === 12">
      <span class="content"
        >{{ msg.msgContent.headText }}{{ msg.msgContent.list.length ? "\n" : ""
        }}{{
          msg.msgContent.list.map((item) => item.click.text).join("\n")
        }}</span
      >
    </div>
    <div v-else-if="msg.msgType === 13">
      <img :src="decryptUrl" alt="" @click="onModleImgClick(decryptUrl)" />
    </div>
    <div v-else-if="msg.msgType === 14">
      <div
        class="w-100 h-20 flex cursor-pointer"
        @click="onAudioPlay"
        title="点击播放音频"
      >
        <div
          class="w-38 h26 bg-[url(http://exam-room.mc-cdn.cn/exam-room/2024/07/10/18/4501eb32eab8496f8e6e414dcc918017.png)] bg-no-repeat bg-center bg-cover"
        ></div>
      </div>
    </div>
    <div v-else-if="msg.msgType === 15">
      <video controls :src="decryptUrl" @play="onVideoPlay"></video>
    </div>
    <div v-else-if="msg.msgType === 16">
      <div
        class="flex cursor-pointer items-center"
        title="点击下载文件"
        @click="onDownFile(decryptUrl, msg.msgContent.name)"
      >
        <span class="break-all">{{ msg.msgContent.name || "未知文件" }} </span>
        <div class="w-20 h-22 ml-15 shrink-0" v-html="$icon_file"></div>
      </div>
    </div>
    <div v-else-if="msg.msgType === 111">
      <span class="content"
        ><span class="msg-pic-icon"></span>{{ prefix
        }}{{ msg.msgContent.name }}</span
      >
    </div>
    <div v-else-if="msg.msgType === 112">
      <span class="content"
        ><span class="msg-file-icon"></span>{{ prefix
        }}{{ msg.msgContent.name }}</span
      >
    </div>
    <div v-else-if="msg.msgType === 113">
      <span class="content"
        ><span class="msg-video-icon"></span>{{ prefix
        }}{{ msg.msgContent.name }}</span
      >
    </div>
    <div v-else-if="msg.msgType === 114">
      <span class="content"
        >【小程序链接】{{ prefix }}{{ msg.msgContent.title }}</span
      >
    </div>
    <div v-else-if="(msg.msgType === 115 || msg.msgType === 10011) && prefix">
      <span class="content">
        【图文链接】{{ prefix
        }}<a
          :href="msg.msgContent.url"
          class="block ml-8"
          :class="revert ? 'text-[#f0e68c]' : 'text-blue-500'"
          target="_blank"
          @click.stop="noop"
          >{{ msg.msgContent.title }}</a
        >
      </span>
    </div>
    <div v-else-if="(msg.msgType === 115 || msg.msgType === 10011) && !prefix">
      <span class="content">
        <a
          class="flex"
          target="_blank"
          @click.stop="noop"
          :href="msg.msgContent.url"
        >
          <img class="w-94 h-94 object-cover" :src="decryptUrl" alt="" />
          <div class="ml-10">
            <div
              style="
                display: -webkit-box;
                -webkit-box-orient: vertical;
                overflow: hidden;
                -webkit-line-clamp: 2;
              "
            >
              {{ msg.msgContent.title }}
            </div>
            <div
              class="opacity-80 text-[0.9em] mt-[0.2em]"
              style="
                display: -webkit-box;
                -webkit-box-orient: vertical;
                overflow: hidden;
                -webkit-line-clamp: 2;
              "
            >
              {{ msg.msgContent.desc }}
            </div>
          </div>
        </a>
      </span>
    </div>
    <div v-else-if="msg.msgType === 10020 && !prefix">
      <span class="content">
        <a
          class="flex flex-col shadow-md"
          :class="terminal ? 'w-full' : 'w-287'"
          target="_blank"
          @click.stop="noop"
          :href="msg.msgContent.url"
        >
          <img
            class="object-cover"
            :class="terminal ? 'h-90' : 'h-123'"
            :src="decryptUrl"
            alt=""
          />
          <div class="bg-white p-8 whitespace-nowrap text-[#565656]">
            <div
              class="overflow-hidden text-ellipsis"
              :title="msg.msgContent.title"
            >
              {{ msg.msgContent.title }}
            </div>
            <div
              class="opacity-80 text-[0.9em] mt-[0.2em] overflow-hidden text-ellipsis"
              :title="msg.msgContent.desc"
            >
              {{ msg.msgContent.desc }}
            </div>
          </div>
        </a>
      </span>
    </div>
    <div v-else-if="msg.msgType === 116">
      <span class="content">{{ prefix }}【{{ msg.msgContent.name }}】</span>
    </div>
    <div v-else-if="msg.msgType === 117">
      <span class="content">{{ prefix }}【{{ msg.msgContent.name }}】</span>
    </div>
    <div v-else-if="msg.msgType === 119">
      <span class="content"
        >【学情分析链接】{{ prefix }}【{{ msg.msgContent.title }}】</span
      >
    </div>
    <div v-else-if="msg.msgType === 10000">
      <span class="content"
        >【报价单消息】<br />
        学员昵称：{{ msg.msgContent.customerName }}<br />
        商品名称：{{ msg.msgContent.productName }}<br />
        课程报价：{{ msg.msgContent.price }}<br />
        <a
          :href="processTestUrl(msg.msgContent.webActionUrl)"
          class="block ml-8"
          :class="revert ? 'text-[#f0e68c]' : 'text-blue-500'"
          target="_blank"
          @click.stop="noop"
          >查看详情</a
        >
      </span>
    </div>
    <div v-else-if="msg.msgType === 10001">
      <span class="content">【快捷指令】{{ msg.msgContent.text }}</span>
    </div>
    <div v-else-if="msg.msgType === 10002">
      <span class="content">【小程序消息】{{ msg.msgContent.title }}</span>
    </div>
    <div v-else-if="msg.msgType === 10003">
      <span class="content"
        >【私教学员信息收集表单消息】{{ msg.msgContent.title }}</span
      >
    </div>
    <div v-else-if="msg.msgType === 10004">
      <span class="content"
        >【学情分析消息】<br />
        车型：小车<br />
        科目：科一<br />
        考试标准：{{
          msg.msgContent.examSummary?.examStandard ? "已达标" : "未达标"
        }}<br />
        模考次数：{{ msg.msgContent.examSummary?.examTimes }}<br />
        近十次模考平均分：{{ msg.msgContent.examSummary?.examAvgScore }}<br />
        <a
          :href="processTestUrl(msg.msgContent.webActionUrl)"
          class="block ml-8"
          :class="revert ? 'text-[#f0e68c]' : 'text-blue-500'"
          target="_blank"
          @click.stop="noop"
          >查看详情</a
        >
      </span>
    </div>
    <div v-else-if="msg.msgType === 10005">
      <span class="content"
        >【支付成功】{{ msg.msgContent.title }}<br />
        订单号：{{ msg.msgContent.orderNo }}
      </span>
    </div>
    <div v-else-if="msg.msgType === 10007">
      <span class="content"
        >【位置】{{ msg.msgContent.name }}<br />
        地址：{{ msg.msgContent.address }}
      </span>
    </div>
    <div v-else-if="msg.msgType === 10008">
      <span class="content"
        >{{ msg.msgContent.text }}<br />
        <span
          v-for="btn in msg.msgContent.btns"
          class="inline-block mr-6 mt-8 py-4 px-10 rounded-full text-white"
          :style="{
            background: btn.color
              ? btn.color.length === 2
                ? `linear-gradient(to right bottom, ${btn.color[0]}, ${btn.color[1]})`
                : btn.color[0]
              : '',
          }"
          :data-actionUrl="btn.actionUrl"
          >{{ btn.name }}</span
        >
      </span>
    </div>
    <div v-else-if="msg.msgType === 10009">
      <span class="content"
        >【优惠券卡片】{{ msg.msgContent.couponTitle }}<br />
        价格：{{ msg.msgContent.price }}<br />
        过期时间：{{ dateFormat(msg.msgContent.expireTime, "yyyy-MM-dd HH:mm")
        }}<br />
        券码：{{ msg.msgContent.couponCode }}
      </span>
    </div>
    <div v-else-if="msg.msgType === 10010">
      <span class="content"
        >{{ msg.msgContent.recommendText }}<br />
        <div
          class="mt-20 w-380 max-w-full rounded-6 bg-blue-200 flex flex-col"
          v-for="table in msg.msgContent.subTableList"
        >
          <div class="text-center font-bold pt-10">{{ table.title }}</div>
          <table class="m-10 bg-white text-center border border-gray-200">
            <tbody>
              <tr
                :class="{ 'font-bold': lineData.isTitle }"
                v-for="lineData in table.rowList"
              >
                <td
                  class="border border-gray-200 px-4 py-6 text-[#565656]"
                  :style="{
                    width:
                      calcPercent(
                        table.columnRatioList[columnIndex],
                        table.columnRatioList,
                      ) + '%',
                  }"
                  v-for="(cellData, columnIndex) in lineData.columnDataList"
                >
                  {{ cellData }}
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </span>
    </div>
    <div v-else-if="msg.msgType === 10012">
      <span class="content"
        >【驾校报名卡片】<br />
        班型：{{ msg.msgContent.className }}<br />
        驾校：{{ msg.msgContent.schoolName }}<br />
        <div class="flex">
          <div class="shrink-0">&nbsp;训练场：</div>
          <div class="flex-1">
            {{ msg.msgContent.trainingFieldAddress }}
            <div class="opacity-80 text-[0.9em]">
              {{ msg.msgContent.distanceText }}&nbsp;&nbsp;{{
                msg.msgContent.trainingFieldAreaName
              }}
            </div>
          </div>
        </div>
        价格：{{ msg.msgContent.price }}元
        <a
          :href="`https://share-m.${isTest ? 'ttt.' : ''}kakamobi.com/activity.kakamobi.com/jiakaobaodian-share-vip/driving-apply-class-detail.html?${objToParams(
            {
              goodsCode: msg.msgContent.goodsCode,
              classCode: msg.msgContent.classCode,
              detailReferer: msg.msgContent.detailReferer,
              jiaxiaoId: msg.msgContent.jiaxiaoId,
            },
          )}`"
          class="block ml-8"
          :class="revert ? 'text-[#f0e68c]' : 'text-blue-500'"
          target="_blank"
          @click.stop="noop"
          >查看详情</a
        >
      </span>
    </div>
    <div v-else-if="msg.msgType === 10021">
      <span class="content">
        <div class="bg-white rounded-15 flex flex-col">
          <!-- 课程基本信息 -->
          <div class="flex" :class="terminal ? 'p-5' : 'p-15'">
            <img
              class="object-cover rounded-6"
              :class="terminal ? 'w-60 h-40' : 'w-133 h-91'"
              :src="msg.msgContent.imgUrl"
              alt=""
            />
            <div class="ml-12 flex-1" :class="terminal ? '' : 'min-w-200'">
              <div
                class="text-[#333] font-bold truncate"
                :class="terminal ? 'text-14' : 'text-21/29'"
              >
                {{ msg.msgContent.className }}
              </div>
              <div
                class="mt-4 text-[#9f9f9f]"
                :class="terminal ? 'text-10' : 'text-18/25'"
              >
                {{ msg.msgContent.trainingFieldLabel
                }}<span class="text-[#0099ff]">{{
                  msg.msgContent.distanceText
                }}</span>
                <span class="ml-4">{{
                  msg.msgContent.trainingFieldAreaName
                }}</span>
              </div>
              <!-- 标签 -->
              <div class="mt-8 flex flex-wrap gap-8 h-30 overflow-hidden">
                <div
                  v-for="label in msg.msgContent.labels"
                  :key="label"
                  class="px-8 py-4 rounded-3 border border-[#d4e6ff] text-[#4a7db3]"
                  :class="terminal ? 'text-10/20' : 'text-14/20'"
                >
                  {{ label }}
                </div>
              </div>
            </div>
          </div>

          <!-- 品牌信息 -->
          <div
            class="p-12"
            style="
              background: linear-gradient(
                118deg,
                #ffe0c3 4%,
                rgba(255, 229, 204, 0.3) 83%
              );
            "
          >
            <div
              class="h-20 rounded-3 flex flex-wrap items-center px-20 gap-20 overflow-hidden"
            >
              <div
                v-for="(brand, index) in msg.msgContent.brandInfoList"
                :key="index"
                class="text-[#A54728] flex items-center"
                :class="terminal ? 'text-10' : 'text-14/20'"
              >
                <img :src="brand.iconUrl" class="w-15 h-15 mr-4" alt="" />
                {{ brand.text }}
              </div>
            </div>
          </div>

          <!-- 价格信息 -->
          <div class="p-15 flex items-end whitespace-nowrap">
            <template v-if="msg.msgContent.teamTplDTO">
              <!-- 拼团模式 -->
              <div class="flex-1 flex gap-15 justify-end">
                <div
                  class="bg-gradient-to-tr from-[#ff6d00] to-[#ff1d1d] rounded-6 p-2"
                >
                  <div class="text-white text-15/21 font-bold px-6 pb-2">
                    单独购买
                  </div>
                  <div
                    class="bg-white rounded-3 h-38 flex items-center justify-center px-6"
                  >
                    <span class="text-[#ff1d1d] text-20/28">¥</span>
                    <span class="text-[#ff1d1d] text-27/38 ml-2">{{
                      msg.msgContent.price
                    }}</span>
                  </div>
                </div>
                <div
                  class="bg-gradient-to-tr from-[#ff6d00] to-[#ff1d1d] rounded-6 p-2"
                >
                  <div class="text-white text-15/21 font-bold px-6 pb-2">
                    {{ msg.msgContent.teamTplDTO.teamSize }}人团优惠{{
                      msg.msgContent.teamTplDTO.discount
                    }}
                  </div>
                  <div
                    class="bg-white rounded-3 h-38 flex items-center justify-center px-6"
                  >
                    <span class="text-[#ff1d1d] text-20/28">¥</span>
                    <span class="text-[#ff1d1d] text-27/38 ml-2">{{
                      msg.msgContent.teamTplDTO.teamPrice
                    }}</span>
                  </div>
                </div>
              </div>
            </template>
            <template v-else>
              <!-- 普通模式 -->
              <div class="flex-1">
                <div class="text-[#ff1d1d] text-30/42 font-bold">
                  <span class="text-20/28">¥</span>
                  {{ msg.msgContent.price }}
                </div>
              </div>
              <div
                class="bg-gradient-to-tr from-[#ff6d00] to-[#ff1d1d] rounded-6 w-105 h-41 flex items-center justify-center"
              >
                <span class="text-white text-20/28 font-bold">立即报名</span>
              </div>
            </template>
          </div>
        </div>
      </span>
    </div>
    <div v-else>
      <span class="content" :data-json="JSON.stringify(msg)"
        >【未知消息类型】</span
      >
    </div>
  </div>
  <Model
    v-if="modelVisibleRef"
    v-model="modelVisibleRef"
    bg="rgba(0,0,0,.7)"
    v-resize="handleSizeChange"
  >
    <div class="w-full h-full flex flex-col justify-center items-center">
      <img
        :src="modelUrlRef"
        alt=""
        class="max-w-600 max-h-600 object-contain"
        :style="{
          maxWidth: modelImgSize.width + 'px',
          maxHeight: modelImgSize.height + 'px',
        }"
      />
    </div>
  </Model>
</template>
<script setup lang="ts">
import { HostNames } from "@/utils/request";
import { downLoadFile, assert, objToParams, isTest } from "@/utils/tools";
import { computed, inject, ref, reactive } from "vue";
import BenzAMRRecoder from "benz-amr-recorder";
import { noop, sum } from "lodash";
import { Msg, MsgAction } from "@/api/chat";
import { dateFormat } from "@/utils/format";
import {
  singleEmojiMapper,
  emojiMapper,
} from "@/components/Common/IconButton/emoji/mapper";
import $icon_file from "@/assets/file.svg?raw";
import Model from "./Model.vue";

interface Resize {
  width: number;
  height: number;
}

const props = defineProps<{
  msg: MsgAction;
  prefix?: string;
  revert?: boolean;
}>();
const terminal = inject<number>("terminal");
const modelVisibleRef = ref<boolean>(false);
const modelUrlRef = ref<string>("");
const modelImgSize = reactive<Resize>({
  width: 0,
  height: 0,
});

const calcPercent = (num: number, nums: number[]) => {
  return ((num / sum(nums)) * 100).toFixed(2);
};

const processEmojiText = (text?: string) => {
  return text?.replace(/(\[([^\[\]\s]+)\])/g, function (input, $1) {
    if (emojiMapper[$1]) {
      return `<img style="height:1.5em;display:inline-block;vertical-align:-0.35em;" src="${emojiMapper[$1]}">`;
    } else {
      return input;
    }
  });
};

const handleSizeChange = ({ width, height }: Resize) => {
  modelImgSize.width = width;
  modelImgSize.height = height;
};

const processAtUsers = (msg: Msg) => {
  assert(msg.msgType === 11 || msg.msgType === 10006);
  return msg.msgContent?.text?.replace(
    /@([^@\s]+)\s/g,
    '<span class="text-[#0E00FF]">@$1&nbsp;</span>',
  );
};

const processTestUrl = (url: string) => {
  if (isTest && url.startsWith("https://share-m.kakamobi.com")) {
    // 替换为 https://share-m.ttt.kakamobi.com
    return url.replace(
      "https://share-m.kakamobi.com",
      "https://share-m.ttt.kakamobi.com",
    );
  } else if (isTest && url.startsWith("https://laofuzi.kakamobi.com")) {
    return url.replace(
      "https://laofuzi.kakamobi.com",
      "https://laofuzi.ttt.kakamobi.com",
    );
  }
  return url;
};

const decryptUrl = computed(() => {
  if (
    props.msg.msgType === 13 ||
    props.msg.msgType === 14 ||
    props.msg.msgType === 15 ||
    props.msg.msgType === 16 ||
    props.msg.msgType === 115 ||
    props.msg.msgType === 10011 ||
    props.msg.msgType === 10020
  ) {
    return `${HostNames.chat}/api/h5/media/get.htm?encodedData=${props.msg.msgContent.encodedData}`;
  }
  return "";
});

let amr: BenzAMRRecoder;
// 未播放 0 播放中 1
// let playStatus = ref(0);

const initAudio = (url: string) => {
  amr = new BenzAMRRecoder();
  amr.initWithUrl(url);
};

const onAudioPlay = () => {
  // 全局只保留一个在播放
  (window as any)._playingAmr?.pause();
  (window as any)._playingVideo?.pause();
  (window as any)._playingAmr = amr;
  // playStatus.value = 1;
  amr.play();
};

const onVideoPlay = (e: Event) => {
  const video = e.currentTarget as HTMLVideoElement;
  // 全局只保留一个在播放
  (window as any)._playingAmr?.pause();
  if ((window as any)._playingVideo !== video) {
    (window as any)._playingVideo?.pause();
  }
  (window as any)._playingVideo = video;
};

// const onAudioPause = () => {
//   playStatus.value = 0;
//   amr.pause();
// }

const onDownFile = (url: string, fileName: string) => {
  downLoadFile(url, fileName);
};

// 服务器传过来的是amr格式的文件流，audio不能直接播放
if (props.msg.msgType === 14) {
  initAudio(
    `${HostNames.chat}/api/h5/media/stream.htm?encodedData=${props.msg.msgContent.encodedData}`,
  );
}

const onModleImgClick = (url: string) => {
  modelUrlRef.value = url;
  modelVisibleRef.value = true;
};
</script>

<style lang="less" scoped>
.msgItemContainer {
  .msg-pic-icon {
    background-image: url(@/assets/pic.svg);
    background-size: auto 100%;
    background-position: center;
    width: 0.3rem;
    height: 0.3rem;
    display: inline-block;
    vertical-align: -5px;
  }

  .msg-file-icon {
    background-image: url(@/assets/file.svg);
    background-size: cover;
    width: 0.34rem;
    height: 0.35rem;
    display: inline-block;
    vertical-align: -6px;
  }

  .msg-video-icon {
    background-image: url(@/assets/video.svg);
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
    width: 0.3rem;
    height: 0.3rem;
    display: inline-block;
    vertical-align: -4px;
  }

  .content {
    display: block;
    font-size: 0.24rem !important;
  }
}
</style>
