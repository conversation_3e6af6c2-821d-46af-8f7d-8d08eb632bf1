{"name": "my-project", "private": true, "version": "0.0.0", "scripts": {"dev": "npx vite", "build": "npx vue-tsc && npx vite build", "build:test": "npx vue-tsc && npx vite build --mode development --sourcemap inline", "preview": "npx vite preview"}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@simplex/simple-base-sso": "3.1.6", "@simplex/simple-core": "4.0.22", "@simplex/simple-secure-upload": "3.6.5", "benz-amr-recorder": "^1.1.5", "lodash": "^4.17.21", "pinia": "^2.1.7", "vconsole": "^3.15.1", "vue": "^3.5.0", "vue-router": "4.1.5"}, "devDependencies": {"@types/lodash": "4.17.6", "@vitejs/plugin-vue": "^5.0.4", "autoprefixer": "^10.4.19", "less": "^4.2.0", "postcss": "^8.4.38", "prettier": "^3.3.1", "pretty-quick": "^4.0.0", "rollup": "4.13.0", "simple-git-hooks": "2.11.1", "tailwindcss": "^3.4.4", "typescript": "^5.2.2", "vite": "5.2.0", "vue-tsc": "2.0.26"}, "simple-git-hooks": {"pre-commit": "npx pretty-quick --staged"}}