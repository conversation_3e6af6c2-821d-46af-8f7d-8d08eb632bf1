<!-- 搜索的出的用户列表 -->
<template>
  <div class="text-[#6e6e6e] text-[0.3rem] py-[.2rem]">
    {{ sessionListRef.label }}
  </div>
  <div
    v-if="sessionListRef.list.length === 0"
    class="h-[1rem] text-[.3rem] text-[#04a5ff] flex flex-col justify-center items-center"
  >
    暂无
  </div>
  <div v-for="item in sessionListRef.list">
    <div
      class="flex items-stretch h-full"
      @click="emit('goSession', item.sessionKey)"
    >
      <img :src="item.avatar" class="w-[.9rem] h-[.9rem] rounded-full" />
      <div
        v-html="item.name"
        class="text-[0.3rem] w-full leading-[.9rem] border-b-1 ml-[0.2rem]"
      ></div>
    </div>
  </div>
  <div
    class="text-[#04a5ff] text-center text-[.3rem] mt-[.2rem]"
    v-if="sessionListRef.list.length !== 0 && sessionList.showViewBut"
    @click="emit('view', sessionList.id)"
  >
    查看全部
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { SearchListSession } from "./type";

const props = defineProps<{
  sessionList: SearchListSession;
}>();

const emit = defineEmits<{
  (e: "view", id: SearchListSession["id"]): void;
  (e: "goSession", key: string): void;
}>();

const showNumber = 5; // 展示数量 全部

const sessionListRef = computed(() => {
  if (!props.sessionList.showViewBut) {
    return props.sessionList;
  }
  const list = [];
  for (let i = 0; i < props.sessionList.list.length; i++) {
    if (i < showNumber) {
      list.push(props.sessionList.list[i]);
    } else {
      break;
    }
  }
  return {
    ...props.sessionList,
    list,
  };
});
</script>

<style scoped></style>
