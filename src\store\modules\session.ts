import { ref } from "vue";
import { defineStore } from "pinia";
import { Session } from "@/api/chat";

type sessionKeys = keyof Session;
export const useSessionStore = defineStore("sessionStore", () => {
  const session = ref<Session>();

  const setAllSession = (s?: Session) => {
    session.value = s;
  };

  function setSingleSession<K extends sessionKeys>(key: K, value: Session[K]) {
    if (session.value) {
      session.value[key] = value;
    }
  }
  return {
    session,
    setAllSession,
    setSingleSession,
  };
});
