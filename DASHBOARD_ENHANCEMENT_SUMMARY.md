# Dashboard 基础信息增强功能实现总结

## 需求概述

在 Dashboard.vue 组件中增加以下基础信息字段的展示：

- 姓名：学员如果创建了放心学的订单，自动同步订单中的学员姓名
- 职业：学员在聊天过程中告知销售，由销售手动录入
- 年龄：学员在聊天过程中告知销售，由销售手动录入
- 性别：学员在聊天过程中告知销售，由销售手动录入
- VIP类型：取学员的VIP类型数据
- 做题数：取用户云同步数据中的累计做题数
- 模考次数：取用户云同步数据中的模考次数
- 近五次模考平均分：计算用户近五次有效模考记录的平均分

## 实现的更改

### 1. API 接口更新 (`src/api/jxbm.ts`)

#### 扩展 MonaDashboard 接口

```typescript
export interface MonaDashboard {
  // ... 原有字段
  /** 姓名 */
  name?: string;
  /** 性别 */
  gender?: number;
  /** 职业 */
  work?: string;
  /** 年龄 */
  age?: number;
  /** 模考次数 */
  mockTimes?: number;
  /** 近五次模考平均分 */
  latestFiveTimeAvgScore?: number;
  /** 总做题数 */
  totalExerciseCount?: number;
  /** VIP类型列表 */
  vipTypes?: string[];
}
```

#### 新增更新用户信息 API

```typescript
export function updateUserInfo(data: {
  sessionKey: string;
  name?: string;
  age?: number;
  work?: string;
  gender?: number;
}) {
  return request<{ value: boolean }>({
    hostName: "mona",
    url: "api/admin/im/modify-user-info.htm",
    data,
    method: "POST",
  });
}
```

### 2. 工具函数更新 (`src/utils/helpers.ts`)

#### 新增映射器

```typescript
export const GenderMapper: Record<number, string> = {
  0: "女",
  1: "男",
  99: "未知",
};

export const formatVipTypes = (vipTypes?: string[]): string => {
  if (!vipTypes || vipTypes.length === 0) {
    return "--";
  }
  return vipTypes.join("、");
};
```

### 3. Dashboard 组件更新 (`src/components/ChatQuick/JXBM/Dashboard.vue`)

#### 新增字段显示

在手机号字段后面按顺序添加了以下字段：

1. **姓名** - 可编辑，带有"编辑"按钮，支持手动录入
2. **职业** - 可编辑，带有"编辑"按钮
3. **年龄** - 可编辑，带有"编辑"按钮
4. **性别** - 可编辑，带有"编辑"按钮，使用单选框选择
5. **VIP类型** - 只读显示，显示用户的VIP类型列表
6. **做题数** - 只读显示，显示用户累计做题数
7. **模考次数** - 只读显示，显示用户模考次数
8. **近五次模考平均分** - 只读显示，显示计算后的平均分

#### 新增编辑功能

- `editField(field, fieldName)` - 通用字段编辑函数，支持姓名、职业和年龄编辑
- `editGender()` - 性别编辑函数，使用单选框界面

#### 编辑功能特点

- 使用弹窗形式进行编辑
- 支持取消操作
- 编辑成功后自动更新界面显示
- 提供成功/失败的提示信息
- 年龄字段使用数字输入框
- 性别字段使用单选框选择

## 字段显示规则

- 所有新增字段在没有数据时显示 `--`
- 性别字段使用 GenderMapper 进行数值到文本的转换
- VIP类型字段使用 formatVipTypes 函数处理数组显示
- 可编辑字段（姓名、职业、年龄、性别）显示蓝色的"编辑"按钮

## 技术实现细节

- 使用 TypeScript 确保类型安全
- 遵循现有代码风格和组件结构
- 使用现有的 prompt 工具函数进行用户交互
- 使用现有的 makeToast 函数进行消息提示
- 保持与现有 API 调用模式的一致性

## API 接口说明

- **更新用户信息接口**：`/api/admin/im/modify-user-info.htm`
  - 方法：POST
  - 参数：sessionKey（必需）、name、age、work、gender（可选）
  - 性别枚举：0=女，1=男，99=未知
  - 返回：`{ value: boolean }`

## 注意事项

1. 后端 API 接口 `/api/admin/im/modify-user-info.htm` 需要实际实现
2. 后端需要在 `api/admin/im/query-dashboard.htm` 接口中返回新增的字段数据
3. 姓名字段支持手动编辑，也可以从订单自动同步
4. VIP类型、做题数、模考次数等数据的获取逻辑需要在后端实现

## 测试建议

1. 测试所有新增字段的正确显示
2. 测试姓名、职业、年龄、性别字段的编辑功能
3. 测试编辑操作的成功和失败场景
4. 测试取消编辑操作
5. 测试数据更新后界面的正确刷新
