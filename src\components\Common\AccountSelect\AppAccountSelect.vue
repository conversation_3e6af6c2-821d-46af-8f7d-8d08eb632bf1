<template>
  <div v-if="account">
    <div class="flex items-center" @click="goAccountSelect">
      <div class="flex items-center box-border justify-between">
        <p class="flex-[1_auto_auto] flex">
          <span class="flex-[1] w-60">当前身份:</span>
          <span class="flex-[1_auto_auto] text-right">{{ account.name }} </span>
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useAccoutSelect } from "./index";
import { useRouter } from "vue-router";
const { account } = useAccoutSelect();
const router = useRouter();
const goAccountSelect = () => {
  router.push({
    name: "AccountSelect",
  });
};
</script>

<style lang="less" scoped></style>
