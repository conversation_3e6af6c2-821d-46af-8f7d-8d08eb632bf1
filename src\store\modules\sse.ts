import { ref } from "vue";
import { defineStore } from "pinia";
import { EventEmitter } from "@/utils/eventEmitter";

export const useSseEventEmitterStore = defineStore(
  "SseEventEmitterStore",
  () => {
    const sseEventEmitter = ref<EventEmitter>();

    const setSseEventEmitter = (s: EventEmitter) => {
      sseEventEmitter.value = s;
    };

    return {
      sseEventEmitter,
      setSseEventEmitter,
    };
  },
);
