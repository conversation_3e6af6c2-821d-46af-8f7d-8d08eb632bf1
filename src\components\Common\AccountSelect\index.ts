import { inject, ref, watch } from "vue";
import { Account, getAccountList } from "@/api/chat";
import { URLParams } from "@/utils/tools";
import { useSessionStore } from "@/store/index";
import { storeToRefs } from "pinia";
import { useAccountStore } from "@/store/index";
import { useRouter } from "vue-router";
export function useAccoutSelect() {
  const { session } = storeToRefs(useSessionStore());
  const { account } = storeToRefs(useAccountStore());
  const router = useRouter();
  const { setAllAccount } = useAccountStore();
  const terminal = inject<number>("terminal");
  const LocalKey = "AccountSelect-" + (URLParams.get("bizCode") || "10001");

  const accountList = ref<Account[]>([]);
  const showPopup = ref(false);
  const selectAccount = (v: Account) => {
    setAllAccount(v);
    showPopup.value = false;
    localStorage.setItem(LocalKey, String(v.id));
    terminal === 1 && router.back();
  };

  watch(
    () => session.value!,
    async () => {
      const res = await getAccountList({
        sessionKey: session.value!.sessionKey,
      });
      accountList.value = res.itemList;
      const userSelectId = localStorage.getItem(LocalKey) || "";
      setAllAccount(
        res.itemList.find((item) => String(item.id) === userSelectId) ||
          res.itemList[0],
      );
    },
    { immediate: true },
  );

  return {
    account,
    accountList,
    showPopup,
    selectAccount,
  };
}
