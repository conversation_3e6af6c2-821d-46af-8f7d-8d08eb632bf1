<template>
  <div class="flex-1">
    <div class="absolute left-1/2 -translate-x-1/2 flex items-center top-20">
      <div
        class="text-17 text-[#464646] font-bold mr-7 w-60 flex justify-end whitespace-nowrap"
      >
        {{ title }}
      </div>
      <div class="w-38 text-11/21 text-center rounded-3 font-bold">
        <div
          v-if="online === true"
          class="bg-[rgba(0,241,103,0.08)] text-[#00D178]"
        >
          在线
        </div>
        <div v-else-if="online === false" class="bg-[#f2f2f2] text-[#a0a0a0]">
          离线
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, inject } from "vue";
import { Session, getOnlineStatus } from "@/api/chat";
import { EventEmitter, useEventEmitter } from "@/utils/eventEmitter";

const title = ref("");
const online = ref<boolean | undefined>();
const emitter = inject<EventEmitter>("emitter")!;

useEventEmitter(
  emitter,
  "selectSession",
  function selectSession(session: Session) {
    if (session) {
      title.value = session.sessionName;
      online.value = undefined;

      // 群聊不显示在线状态
      if (session.isGroup) {
        return;
      }

      getOnlineStatus({
        sessionKey: session.sessionKey,
      }).then(({ value }) => {
        online.value = value;
      });
    } else {
      title.value = "";
      online.value = undefined;
    }
  },
);
</script>
