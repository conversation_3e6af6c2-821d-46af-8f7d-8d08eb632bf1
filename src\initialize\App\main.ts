import { createApp } from "vue";
import "@/style.css";
import App from "./App.vue";
import router from "./router/index";
import store from "@/store/index";

import { init } from "@/initialize/init";
import { EventEmitter } from "@/utils/eventEmitter";
import resize from "@/directives/resize";
const terminal: number = init();

const app = createApp(App);
app.provide("terminal", terminal);
app.provide("emitter", new EventEmitter());
app.directive("resize", resize);
app.use(router);
app.use(store);
app.mount("#app");

// 拦截a标签点击事件，跳新的iframe页
document.addEventListener(
  "click",
  (ev) => {
    const target = ev.target as HTMLElement;
    if (!target) {
      return;
    }

    // 使用 closest 方法查找最近的 <a> 标签, 包含自身
    const ele = target.closest("a");

    // 只拦截http跳转，不包括下载文件
    if (ele && ele.href && ele.href.startsWith("http") && !ele.download) {
      // 微信公众号网页不允许嵌入iframe
      if (ele.href.startsWith("https://mp.weixin.qq.com/")) {
        return;
      }

      ev.preventDefault();
      router.push({
        name: "WebPage",
        query: {
          src: ele.href,
        },
      });
    }
  },
  true,
);
/**
 * 用于h5页面输入框获取焦点后，当用户触摸了屏幕上的其他地方，输入框失去焦点
 */
document.addEventListener("touchstart", function (event) {
  // 获取当前获得焦点的元素
  const activeElement = document.activeElement;

  // 检查当前焦点元素是否是 input 或 textarea
  if (
    activeElement &&
    (activeElement.tagName.toLowerCase() === "input" ||
      activeElement.tagName.toLowerCase() === "textarea") &&
    activeElement !== event.target
  ) {
    // 手动触发失焦 用于延迟失焦触发，因为点击输入库其他区域，可能会触发点击事件，但是触发了失焦事件，导致点击事件失效
    setTimeout(() => (activeElement as HTMLInputElement).blur(), 100);
  }
});
