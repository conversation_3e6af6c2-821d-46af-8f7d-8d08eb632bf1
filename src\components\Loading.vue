<template>
  <div
    class="absolute left-0 top-0 right-0 bottom-0 w-full h-full bg-[rgba(0,0,0,0.3)] text-white text-20 flex flex-col items-center justify-center rounded-10"
    style="z-index: 99999999"
    v-if="isLoading"
  >
    <div
      class="w-80 h-80 rounded-full border-white border-4 border-r-gray-200 animate-spin"
    ></div>
    <div class="mt-20">{{ loadingText }}</div>
  </div>
</template>

<script setup lang="ts">
withDefaults(
  defineProps<{
    isLoading?: boolean;
    loadingText?: string;
  }>(),
  {
    isLoading: false,
    loadingText: "加载中",
  },
);
</script>

<style scoped lang="less"></style>
