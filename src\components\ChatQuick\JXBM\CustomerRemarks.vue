<template>
  <!-- 客户备注按钮 -->
  <span class="text-[#04a5ff] cursor-pointer" @click="showUserRemarks"
    >查看</span
  >

  <!-- 客户备注弹窗 -->
  <Model v-model="showRemarksModal" bg="rgba(0,0,0,.3)">
    <div
      class="relative bg-white overflow-hidden"
      :class="{
        'w-[600px] rounded-lg': !terminal,
        'w-[90vw] rounded-[5px]': terminal,
      }"
      style="line-height: 1.4"
    >
      <!-- Header with title and close button -->
      <div
        class="flex justify-between items-center border-b border-gray-200 p-10"
        :class="{
          'p-[20px]': !terminal,
          'p-[.3rem]': terminal,
        }"
      >
        <div
          class="font-bold"
          :class="{
            'text-[18px]': !terminal,
            'text-[.36rem]': terminal,
          }"
        >
          客户备注
        </div>
        <div
          class="cursor-pointer absolute"
          :class="{
            'right-[20px] top-[20px]': !terminal,
            'right-[.3rem] top-[.3rem]': terminal,
          }"
          @click="showRemarksModal = false"
        >
          <div
            :class="{
              'w-[24px] h-[24px]': !terminal,
              'w-[.48rem] h-[.48rem]': terminal,
            }"
            v-html="$icon_close"
          ></div>
        </div>
      </div>

      <!-- Content -->
      <div class="p-10 pb-40">
        <div class="flex justify-end mb-10">
          <button
            class="bg-[#04a5ff] text-white hover:bg-blue-600"
            :class="{
              'px-[15px] py-[8px] text-[14px] rounded-lg': !terminal,
              'px-[.3rem] py-[.16rem] text-[.28rem] rounded-[5px]': terminal,
            }"
            @click="showCreateRemark"
          >
            添加
          </button>
        </div>

        <div v-if="loadingRemarks" class="text-center py-20">加载中...</div>
        <div v-else-if="userRemarks.length === 0" class="text-center py-20">
          暂无备注
        </div>
        <div
          v-else
          class="overflow-auto"
          :class="{ 'max-h-[400px]': !terminal, 'max-h-[60vh]': terminal }"
        >
          <table class="w-full border-collapse">
            <thead>
              <tr class="bg-[#f6f9fb]">
                <th class="border border-[#e6eef4] px-8 py-6 text-left">
                  备注内容
                </th>
                <th class="border border-[#e6eef4] px-8 py-6 text-left">
                  创建人
                </th>
                <th class="border border-[#e6eef4] px-8 py-6 text-left">
                  创建时间
                </th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(item, index) in userRemarks" :key="index">
                <td class="border border-[#e6eef4] px-8 py-6">
                  {{ item.remark }}
                </td>
                <td class="border border-[#e6eef4] px-8 py-6">
                  {{ item.createUserName }}
                </td>
                <td class="border border-[#e6eef4] px-8 py-6">
                  {{ dateFormat(item.createTime, "yyyy-MM-dd HH:mm") }}
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </Model>

  <!-- 添加客户备注弹窗 -->
  <Model v-model="showAddRemarkModal" bg="rgba(0,0,0,.3)">
    <div
      class="relative bg-white overflow-hidden"
      :class="{
        'w-[500px] rounded-lg': !terminal,
        'w-[90vw] rounded-[5px]': terminal,
      }"
      style="line-height: 1.4"
    >
      <!-- Header with title and close button -->
      <div
        class="flex justify-between items-center border-b border-gray-200"
        :class="{
          'p-[20px]': !terminal,
          'p-[.3rem]': terminal,
        }"
      >
        <div
          class="font-bold"
          :class="{
            'text-[18px]': !terminal,
            'text-[.36rem]': terminal,
          }"
        >
          添加客户备注
        </div>
        <div
          class="cursor-pointer absolute"
          :class="{
            'right-[20px] top-[20px]': !terminal,
            'right-[.3rem] top-[.3rem]': terminal,
          }"
          @click="showAddRemarkModal = false"
        >
          <div
            :class="{
              'w-[24px] h-[24px]': !terminal,
              'w-[.48rem] h-[.48rem]': terminal,
            }"
            v-html="$icon_close"
          ></div>
        </div>
      </div>

      <!-- Content -->
      <div
        :class="{
          'p-[20px]': !terminal,
          'p-[.3rem]': terminal,
        }"
      >
        <div class="mb-10">
          <div class="mb-8 font-medium">备注内容：</div>
          <textarea
            v-model="newRemarkContent"
            :class="{
              'w-full p-8 border border-[#e6eef4] rounded-8 min-h-[120px]':
                !terminal,
              'w-full p-[.16rem] border border-[#e6eef4] rounded-[.16rem] min-h-[2.4rem]':
                terminal,
            }"
            placeholder="请输入备注内容"
          ></textarea>
        </div>
      </div>

      <!-- Footer with buttons -->
      <div
        class="flex justify-end border-t border-gray-200 space-x-[10px]"
        :class="{
          'p-[20px]': !terminal,
          'p-[.3rem]': terminal,
        }"
      >
        <button
          class="bg-white border border-gray-300 text-gray-700 hover:bg-gray-100"
          :class="{
            'px-[15px] py-[8px] text-[14px] rounded-lg': !terminal,
            'px-[.3rem] py-[.16rem] text-[.28rem] rounded-[5px]': terminal,
          }"
          @click="showAddRemarkModal = false"
        >
          取消
        </button>
        <button
          class="bg-[#04a5ff] text-white hover:bg-blue-600"
          :class="{
            'px-[15px] py-[8px] text-[14px] rounded-lg': !terminal,
            'px-[.3rem] py-[.16rem] text-[.28rem] rounded-[5px]': terminal,
          }"
          :disabled="!newRemarkContent.trim() || addingRemark"
          @click="addUserRemark"
        >
          <span v-if="addingRemark">添加中...</span>
          <span v-else>确定</span>
        </button>
      </div>
    </div>
  </Model>
</template>

<script lang="ts" setup>
import { inject, ref, defineProps } from "vue";
import { UserRemarkItem, getUserRemarks, createUserRemark } from "@/api/jxbm";
import { makeToast } from "@/utils/dom";
import { dateFormat } from "@/utils/format";
import Model from "@/components/Model.vue";
import $icon_close from "@/assets/close.svg?raw";

const props = defineProps<{
  userNo: string;
}>();

const terminal = inject("terminal") as 0 | 1;

// 客户备注相关
const showRemarksModal = ref(false);
const loadingRemarks = ref(false);
const userRemarks = ref<UserRemarkItem[]>([]);
const showAddRemarkModal = ref(false);
const newRemarkContent = ref("");
const addingRemark = ref(false);

/**
 * 显示用户备注
 */
const showUserRemarks = async () => {
  if (!props.userNo) {
    makeToast("未获取到用户编号");
    return;
  }

  showRemarksModal.value = true;
  loadingRemarks.value = true;
  userRemarks.value = [];

  try {
    userRemarks.value = await getUserRemarks({
      userNo: props.userNo,
    });
  } catch (error) {
    console.error("获取用户备注失败", error);
    makeToast("获取用户备注失败");
  } finally {
    loadingRemarks.value = false;
  }
};

const showCreateRemark = () => {
  showAddRemarkModal.value = true;
  newRemarkContent.value = "";
};

/**
 * 添加用户备注
 */
const addUserRemark = async () => {
  if (!props.userNo) {
    makeToast("未获取到用户编号");
    return;
  }

  if (!newRemarkContent.value.trim()) {
    makeToast("请输入备注内容");
    return;
  }

  addingRemark.value = true;

  try {
    await createUserRemark({
      userNo: props.userNo,
      remark: newRemarkContent.value.trim(),
    });

    makeToast("添加备注成功");
    showAddRemarkModal.value = false;

    // 刷新备注列表
    showUserRemarks();
  } catch (error) {
    console.error("添加用户备注失败", error);
    makeToast("添加备注失败");
  } finally {
    addingRemark.value = false;
  }
};
</script>
