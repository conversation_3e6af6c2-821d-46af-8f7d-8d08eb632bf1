<!-- 管理分组 -->
<template>
  <div class="flex flex-col px-8">
    <div class="flex gap-x-10 justify-between">
      <div
        class="flex-1 bg-[#f2faff] rounded-4"
        v-for="regionItem in regionList"
        :key="regionItem.id"
      >
        <p
          class="text-[#6e6e6e] text-center"
          :class="{
            'text-12/23 h-23': !terminal,
            'text-[.3rem]/[.4rem] h-[.6rem]': terminal,
          }"
        >
          {{ regionItem.title }}
        </p>
        <div v-for="item in regionItem.tab" :key="item.tagCode">
          <div
            class="flex justify-between text-[#333333] items-center"
            :class="{
              'text-13 h-36 px-14': !terminal,
              'text-[.3rem] h-[.55rem] px-[.14rem]': terminal,
            }"
          >
            <div>
              {{ item.tagName }}
            </div>
            <img
              v-if="item.tagCode === 'session-list-all'"
              src="@/assets/left_jian_1.png"
              alt=""
              :class="{
                'w-17 h-17': !terminal,
                'w-[.4rem] h-[.4rem]': terminal,
              }"
            />
            <img
              v-else
              :src="regionItem.url"
              class="cursor-pointer"
              :class="{
                'w-17 h-17 ': !terminal,
                'w-[.4rem] h-[.4rem]': terminal,
              }"
              @click.stop="regionItem.onClick(item)"
            />
          </div>
        </div>
      </div>
    </div>
    <div class="flex self-end pt-8 pb-10">
      <div
        class="bg-[#ffffff] border-1 border-solid border-[#cccfd0] mr-10 text-center text-[#464646] cursor-pointer"
        @click.stop="switchPop = 1"
        :class="{
          'w-79 h-32 mr-10 rounded-8 leading-32 text-14': !terminal,
          'w-[1.5rem] h-[.6rem] rounded-[.1rem] text-[.3rem]/[.6rem]': terminal,
        }"
      >
        取消
      </div>
      <div
        class="bg-[#04a5ff] text-center text-[#ffffff] cursor-pointer"
        :class="{
          'w-79 h-32 mr-10 rounded-8 leading-32 text-14': !terminal,
          'w-[1.5rem] h-[.6rem] rounded-[.1rem] text-[.3rem]/[.6rem]': terminal,
        }"
        @click="onConfirmClick"
      >
        确定
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Tabs } from "@/api/chat";
import leftJao from "@/assets/left_jia.png";
import leftJian from "@/assets/left_jian.png";
import { ref, toRaw } from "vue";
import { inject } from "vue";
const emit = defineEmits<{
  (e: "confirm"): void;
}>();

interface RegionList {
  id: number;
  title: string;
  url: string;
  onClick: (item: Tabs) => void;
  tab: Set<Tabs>;
}

const sessionTab = defineModel<Set<Tabs>>("sessionTab");
const addSessionTab = defineModel<Set<Tabs>>("addSessionTab");
const switchPop = defineModel<number>("switchPop");
const terminal = inject("terminal");

const copySessionTab = ref<Set<Tabs>>(
  new Set([...toRaw(sessionTab.value || [])]),
);
const copyAddSessionTab = ref<Set<Tabs>>(
  new Set([...toRaw(addSessionTab.value || [])]),
);
const regionList: RegionList[] = [
  {
    id: 1,
    title: "默认分组",
    url: leftJian,
    tab: copySessionTab.value,
    onClick: (item: Tabs) => {
      copySessionTab.value?.delete(item);
      copyAddSessionTab.value?.add(item);
    },
  },
  {
    id: 2,
    title: "可添加",
    url: leftJao,
    tab: copyAddSessionTab.value,
    onClick: (item: Tabs) => {
      copyAddSessionTab.value?.delete(item);
      copySessionTab.value?.add(item);
    },
  },
];
const onConfirmClick = () => {
  sessionTab.value = copySessionTab.value;
  addSessionTab.value = copyAddSessionTab.value;
  switchPop.value = 1;
  emit("confirm");
};
</script>

<style scoped lang="less"></style>
