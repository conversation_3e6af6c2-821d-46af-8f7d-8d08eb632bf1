<template>
  <div
    class="relative"
    @mouseover="showMenu = true"
    @mouseleave="showMenu = false"
  >
    <div
      class="w-22 h-22 bg-[url(./assets/body_more.png)] rounded-5 text-center text-[#6e6e6e] flex items-center justify-center popup-pseudo bg-cover"
      :class="{
        'after:left-10': !isInRight && !terminal,
        'after:right-10': isInRight && !terminal,
      }"
    ></div>
    <div
      v-if="showMenu"
      class="absolute z-1 bg-[#f3f9fd] border border-gray-200 shadow-lg w-75 box-border"
      :class="{
        'left-26 -top-22': !isInRight && !terminal,
        'right-26 -top-22': isInRight && !terminal,
        'bottom-26 left-0': isInRight && terminal,
        'bottom-26 right-0': !isInRight && terminal,
        'rounded-md': !terminal,
        'rounded-10': terminal,
      }"
      ref="contextMenuRef"
    >
      <div
        class="cursor-pointer text-12 h-26 text-[#6e6e6e] text-center whitespace-nowrap text-ellipsis overflow-hidden leading-26 px-2"
        :title="item.label"
        @click="handleClick(item)"
        v-for="item in menu"
        :key="item.label"
      >
        <div
          class="hover:bg-[#ffffff] hover:rounded-[3.57px] hover:[box-shadow:0px_0.89px_3.57px_0px_rgba(235,232,232,0.50)] w-full h-full"
        >
          {{ item.label }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { inject, ref } from "vue";

export interface MenuItem {
  label: string;
  disabled?: boolean;
  onClick?: () => void;
}

defineProps<{
  menu: MenuItem[];
  isInRight: boolean;
}>();

const emit = defineEmits<{
  (e: "select", item: any): void;
}>();

const terminal = inject("terminal");
const showMenu = ref(false);

const handleClick = (item: MenuItem) => {
  // 选中菜单后关闭菜单
  showMenu.value = false;
  item.onClick?.();
  // 并返回选中的菜单
  emit("select", item);
};
</script>

<style scoped lang="less">
.popup-pseudo {
  &::after {
    position: absolute;

    content: "";
    display: block;
    width: 100%;
    height: 100%;
    background-color: transparent;
  }
}
</style>
