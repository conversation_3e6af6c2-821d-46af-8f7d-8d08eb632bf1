<template>
  <div class="bg-[#e6f2fb] rounded-14 pb-10" v-if="groupMembersList.length > 0">
    <div class="items-center h-50 flex">
      <i
        class="bg-[#04a5ff] rounded-r-2 mr-10"
        :class="terminal ? 'w-[.05rem] h-[.5rem]' : 'w-3 h-14'"
      ></i>
      <span
        :class="terminal ? 'text-[.3rem]' : 'text-14/20'"
        class="font-bold text-[rgba(0,0,0,0.85)] flex-1"
        >电话提醒</span
      >
    </div>
    <div
      v-for="item in listRef"
      :key="item.id"
      :class="terminal ? 'text-[.3rem]' : 'text-14/20'"
      class="m-10 bg-[#F2FAFF] rounded-5"
    >
      <div class="p-10">
        <div class="flex">
          <span>电话提醒</span>
          <span
            :style="{
              color: item.color,
            }"
            >{{ item.label }}</span
          >
        </div>
        <div class="mt-10">
          <button
            :disabled="item.disabled"
            :class="item.class"
            class="p-10 rounded-5"
            @click="item.onClick"
          >
            {{ item.butText }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { inject, ref } from "vue";
import { prompt, makeToast } from "@/utils/dom";
import { notifyLecturerToTeach, notifyStudentToTeach } from "@/api/groupChat";
import { GroupMemberList } from "@/api/groupChat";
import phoneRemindClass from "../../utils/phoneRemind";
import { storeToRefs } from "pinia";
import { useSessionStore } from "@/store";
import { watch } from "vue";

interface List {
  id: number;
  label: string;
  color: string;
  disabled: boolean;
  class: string;
  butText: string;
  onClick: () => void;
}

const props = defineProps<{
  groupMembersList: GroupMemberList[];
}>();
const { session } = storeToRefs(useSessionStore());
const terminal = inject<0 | 1>("terminal");
const listRef = ref<List[]>([]);

/**
 * 处理数据
 */
const handleList = () => {
  return [
    {
      id: 1,
      label: "讲师上课",
      butText: handleButtonStyle(4).text,
      color: "#FC6162",
      disabled: handleButtonDisabled(),
      class: handleButtonStyle(4).class,
      onClick: () => {
        if (!session.value!.sessionKey) return makeToast("未获取到会话");
        if (!handleName(4)) return makeToast("没有讲师");
        const phoneRemindDraggableDom = document.querySelector(
          ".phone-remind-container",
        );
        if (phoneRemindDraggableDom) {
          phoneRemindDraggableDom.remove();
        }
        prompt({
          title: `确认给${handleName(4)}讲师拨打电话吗？`,
          terminal,
        }).then(async () => {
          listRef.value[0].disabled = true;
          const res = await notifyLecturerToTeach(
            session.value!.sessionKey,
          ).finally(() => {
            listRef.value[0].disabled = false;
          });
          if (res.value) {
            phoneRemindClass.setPhoneInfo(
              session.value!.sessionKey,
              true,
              4,
              handleName(4)!,
              session.value!.sessionName,
              res.value,
            );
            listRef.value = handleList();
            phoneRemindClass.handleCallStatus(() => {
              listRef.value = handleList();
            });
          }
        });
      },
    },
    {
      id: 2,
      label: "学员约课",
      color: "#3AA4FF",
      class: handleButtonStyle(5).class,
      disabled: handleButtonDisabled(),
      butText: handleButtonStyle(5).text,
      onClick: () => {
        if (!session.value!.sessionKey) return makeToast("未获取到会话");
        if (!handleName(5)) return makeToast("没有学员");
        const phoneRemindDraggableDom = document.querySelector(
          ".phone-remind-container",
        );
        if (phoneRemindDraggableDom) {
          phoneRemindDraggableDom.remove();
        }
        prompt({
          title: `确认给${handleName(5)}学员拨打电话吗？`,
          terminal,
        }).then(async () => {
          listRef.value[1].disabled = true;
          const res = await notifyStudentToTeach(
            session.value!.sessionKey,
          ).finally(() => {
            listRef.value[1].disabled = false;
          });
          if (res.value) {
            phoneRemindClass.setPhoneInfo(
              session.value!.sessionKey,
              true,
              5,
              handleName(5)!,
              session.value!.sessionName,
              res.value,
            );
            listRef.value = handleList();
            phoneRemindClass.handleCallStatus(() => {
              listRef.value = handleList();
            });
          }
        });
      },
    },
  ];
};

/**
 * 处理名称
 * @param role 身份
 */
const handleName = (role: 4 | 5) => {
  if (props.groupMembersList.length === 0) {
    return;
  }
  return props.groupMembersList.find((item) => item.role === role)?.nickname;
};

/**
 * 处理按钮文本和样式状态
 */
const handleButtonStyle = (role: 4 | 5) => {
  if (
    phoneRemindClass.getIsRemind() &&
    phoneRemindClass.getRole() === role &&
    phoneRemindClass.getSessionKey() === session.value!.sessionKey
  ) {
    return {
      text: `${handleName(role)}呼叫中`,
      class: "call-waiting",
    };
  } else {
    return {
      text: "点击拨打电话",
      class: "call-not-started",
    };
  }
};

/**
 * 处理按钮禁用状态
 */
const handleButtonDisabled = () => {
  if (phoneRemindClass.getIsRemind()) {
    return true;
  }
  return false;
};

watch(
  () => props.groupMembersList,
  () => {
    listRef.value = handleList();
  },
  {
    immediate: true,
    deep: true,
  },
);
</script>

<style scoped lang="less"></style>

<style>
.call-not-started {
  background-color: #04a5ff;
  color: #fff;
}

.call-waiting {
  background-color: #83cbfd;
  color: #edf6fe;
}
</style>
