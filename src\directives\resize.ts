const map = new WeakMap();
const ob = new ResizeObserver((entries) => {
  for (const entry of entries) {
    // 获取dom元素的回调
    const handler = map.get(entry.target);
    //存在回调函数
    if (handler) {
      // 将监听的值给回调函数
      handler({
        width: (entry.target as HTMLElement).offsetWidth,
        height: (entry.target as HTMLElement).offsetHeight,
      });
    }
  }
});

export default {
  mounted(el: HTMLElement, binding: any) {
    //将dom与回调的关系塞入map
    map.set(el, binding.value);
    //监听el元素的变化
    ob.observe(el);
  },
  unmounted(el: HTMLElement) {
    //取消监听
    ob.unobserve(el);
  },
};
