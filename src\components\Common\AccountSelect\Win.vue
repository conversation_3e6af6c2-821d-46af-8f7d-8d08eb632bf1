<template>
  <div class="relative self-start" v-if="account">
    <div
      class="flex items-center cursor-pointer"
      @click.stop="showPopup = !showPopup"
    >
      <div
        class="border-[0.5px] border-solid border-[#04a5ff] rounded-[6px] h-26 text-12 text-[#04a5ff] leading-28 flex items-center px-8 py-0 box-border justify-between"
      >
        <p class="flex-[1_auto_auto] flex">
          <span class="flex-[1] w-60">当前身份:</span>
          <span class="flex-[1_auto_auto] text-right">{{ account.name }} </span>
        </p>

        <div
          class="ml-5 w-13 h-12 bg-cover bg-[url(./assets/body_refresh.png)]"
        ></div>
      </div>
    </div>
    <Popup
      class="absolute z-1 bottom-full left-0 w-full rounded-8 overflow-hidden shadow-lg mb-6 bg-white box-border p-3 flex flex-col gap-y-6 border-[1px] border-solid border-[#cccfd0]"
      v-model="showPopup"
    >
      <div
        class="flex items-center hover:bg-[#e6f2fb] hover:cursor-pointer h-38 box-border rounded-4 px-8"
        :class="{
          'bg-[#e6f2fb] text-[#04a5ff]': item.id === account.id,
        }"
        v-for="item in accountList"
        :key="item.id"
        @click="selectAccount(item)"
      >
        <img
          class="w-26 h-26 rounded-full object-cover"
          style="max-width: unset"
          :src="item.avatar"
        />
        <div
          class="ml-8 overflow-hidden text-ellipsis whitespace-nowrap max-w-100 text-12"
          :title="item.name"
        >
          {{ item.name }}
        </div>
      </div>
    </Popup>
  </div>
</template>

<script setup lang="ts">
import Popup from "@/components/Popup.vue";
import { useAccoutSelect } from "./index";
const { account, showPopup, accountList, selectAccount } = useAccoutSelect();
</script>

<style lang="scss" scoped></style>
