<template>
  <div
    class="flex flex-col"
    :class="terminal ? 'virtualList' : 'ml-10 mb-9 w-320'"
  >
    <SessionTab @switchTab="switchTab" ref="sessionTabRef"></SessionTab>
    <div class="p-5 bg-white rounded-14 h-0 flex-1">
      <div class="h-full overflow-hidden rounded-12">
        <VirtualList
          class="h-full"
          @scroll="onScroll"
          :items="sessions.itemList"
          ref="virtualListRef"
          :item-key="(item: Session) => item.sessionKey"
          :item-height="70"
        >
          <template #default="{ item: session }">
            <ContextMenu
              :menu="[
                {
                  label: () => '标记为已处理',
                  onClick: () => handleMarkProcessedSession(session),
                  disabled: () => sessionListType !== 111,
                },
                {
                  label: () => '标记为已回复',
                  onClick: () => handleMarkRepliedSession(session),
                  disabled: () =>
                    appStore.sessionTab !== 'session-list-un-reply',
                },
                {
                  label: () => (session.isPinned ? '取消置顶' : '消息置顶'),
                  icon: () => (session.isPinned ? $icon_top : $icon_top2),
                  onClick: () => handlePinnedSession(session),
                },
                {
                  label: () =>
                    isNoDisturb(session) ? '新消息提醒' : '消息免打扰',
                  icon: () => (isNoDisturb(session) ? $icon_bell2 : $icon_bell),

                  onClick: () => handleNoDisturbSession(session),
                },
              ]"
            >
              <div
                :class="{
                  sessionActive: session === selectSession && !terminal,
                  item: terminal,
                  sessionPinned: session.isPinned,
                  sessionPinnedFirst: session === sessionsPinned.first,
                  sessionPinnedLast: session === sessionsPinned.last,
                  'sessionHover relative flex rounded-12': !terminal,
                }"
                @click="handleSelectSession(session, false)"
              >
                <img
                  :src="session.avatar"
                  :class="
                    !!terminal
                      ? 'avatar'
                      : 'w-50 h-50 rounded-full m-12 mr-10 object-cover'
                  "
                />

                <div
                  :class="
                    !!terminal
                      ? 'content'
                      : 'flex-1 flex flex-col justify-center'
                  "
                >
                  <div
                    :class="
                      !!terminal
                        ? 'sessionName'
                        : 'w-120 mb-3 text-16/22 text-[#464646] overflow-hidden whitespace-nowrap text-ellipsis title'
                    "
                    :title="session.sessionName"
                  >
                    {{ session.sessionName }}
                  </div>
                  <div
                    :class="
                      !!terminal
                        ? 'describe'
                        : 'w-188 text-12/18 h-16 text-[#888888] overflow-hidden whitespace-nowrap text-ellipsis'
                    "
                  >
                    {{
                      session.latestMsg?.staff &&
                      session.latestMsg?.readReceipts &&
                      session !== selectSession
                        ? "[已读]"
                        : ""
                    }}{{
                      session.isGroup &&
                      session.latestMsg?.customer &&
                      !isSystemMsg(session.latestMsg)
                        ? session.latestMsg?.customer?.nickname + "："
                        : ""
                    }}{{ session.newMsg }}
                  </div>
                </div>
                <div
                  :class="
                    !!terminal
                      ? 'updateTime'
                      : 'absolute right-12 top-16 text-12/18 text-[#888888]'
                  "
                >
                  {{ formatSessionDate(session.updateTime) }}
                </div>
                <div
                  :class="
                    !!terminal
                      ? 'newMsgCount'
                      : 'absolute bottom-16 right-10 w-20 text-13/20 h-20 text-white text-center rounded-full bg-[#ff4a40]'
                  "
                  v-if="session.newMsgCount && !isNoDisturb(session)"
                >
                  <div :style="{ scale: !!terminal ? 1 : 0.8 }">
                    {{ session.newMsgCount > 99 ? "99+" : session.newMsgCount }}
                  </div>
                </div>
                <div
                  class="absolute bottom-12 right-10 flex items-center"
                  v-if="isNoDisturb(session)"
                >
                  <div
                    class="bg-cover w-22 h-22 bg-[url(./assets/bell.svg)]"
                  ></div>
                  <div
                    class="ml-4 w-6 h-6 rounded-full bg-[#ff4a40]"
                    v-if="session.newMsgCount"
                  ></div>
                </div>
              </div>
            </ContextMenu>
          </template>
        </VirtualList>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { EventEmitter, useEventEmitter } from "@/utils/eventEmitter";
import h5Hash from "@/utils/h5Hash";
import {
  Msg,
  Session,
  getSessionList,
  getSessionDetail,
  Pagination,
  markProcessed,
  markReplied,
  SessionListType,
  getPinnedSessionList,
  updatePinnedStatus,
  getNoDisturbSessionList,
} from "@/api/chat";
import {
  onMounted,
  onUnmounted,
  ref,
  watch,
  reactive,
  inject,
  computed,
  nextTick,
} from "vue";
import { humanKindDateFormat } from "@/utils/format";
import VirtualList from "./VirtualList.vue";
import ContextMenu from "./ContextMenu.vue";
import SessionTab from "./SessionTab.vue";
import { makeToast } from "@/utils/dom";
import { useAppStore } from "@/store";
import { getMsgHint, isSystemMsg } from "@/utils/helpers";
import { uniqBy } from "lodash";
import { remove } from "@/utils/tools";
import { updateNoDisturbStatus } from "@/api/chat";
import $icon_bell from "@/assets/bell.svg?raw";
import $icon_bell2 from "@/assets/bell2.svg?raw";
import $icon_top from "@/assets/top.svg?raw";
import $icon_top2 from "@/assets/top2.svg?raw";
import { prompt } from "@/utils/dom";

const props = defineProps<{
  sse?: EventEmitter;
}>();

const emit = defineEmits<{
  (e: "selectSession", session?: Session): void;
}>();

defineExpose({
  refresh() {
    fetchSessions();
    scrollToTop();
  },
  removeCurrent() {
    if (sessionListType.value === 111 && selectSession.value) {
      handleMarkProcessedSession(selectSession.value);
    }
  },
});

const emitter = inject<EventEmitter>("emitter")!;

const sessionTabRef = ref<InstanceType<typeof SessionTab>>();
const appStore = useAppStore();
const switchTab = (tab: string) => {
  if (appStore.sessionTab === tab) return;
  appStore.sessionTab = tab;
  fetchSessions();
};
const sessionListType = computed<SessionListType>(() => {
  return 101;
});

const terminal: number | undefined = inject("terminal");
const sessions = reactive<Pagination<Session>>({
  cursor: "",
  hasMore: false,
  itemList: [],
  needClean: false,
});
const selectSession = ref<Session>();
const sessionsPinned = computed<{
  first?: Session;
  last?: Session;
}>(() => {
  const list = sessions.itemList.filter((item) => item.isPinned);
  return {
    first: list[0],
    last: list[list.length - 1],
  };
});

const formatSessionDate = (date: number) => humanKindDateFormat(date);

const prefix = "#session-";

const handleSelectSession = async (session?: Session, locate = true) => {
  selectSession.value = session;
  emit("selectSession", session);
  emitter.emit("selectSession", session);
  if (session) {
    session.__markMsgRead = session.newMsgCount > 0;
    session.newMsgCount = 0;
    terminal === 0 ? (location.hash = prefix + session.sessionKey) : "";
    if (locate) {
      nextTick(() => {
        virtualListRef.value!.scrollToItem(session);
      });
    }
  } else {
    location.hash = "";
  }
};

const virtualListRef = ref<InstanceType<typeof VirtualList>>();
const scrollToTop = () => {
  const scroller = virtualListRef.value!.containerRef!;
  scroller.scrollTo({
    top: 0,
    behavior: "instant",
  });
};

const noDisturbList = ref<string[]>([]);
const isNoDisturb = (session: Session) =>
  noDisturbList.value.includes(session.sessionKey);

let initSessionFromHash = true;
const fetchSessions = async () => {
  getNoDisturbSessionList({
    tagCode: appStore.sessionTab,
  }).then((res) => (noDisturbList.value = res.itemList));

  const [res, res2] = await Promise.all([
    getSessionList({
      sessionListType: sessionListType.value,
      tagCode: appStore.sessionTab,
    }),
    getPinnedSessionList({
      tagCode: appStore.sessionTab,
    }),
  ]);
  res.itemList = uniqBy(
    [
      ...res2.itemList.map((item) => {
        item.isPinned = true;
        return item;
      }),
      ...res.itemList,
    ],
    (s) => s.sessionKey,
  );
  Object.assign(sessions, res);
  res.itemList.forEach((session) => {
    if (session.latestMsg) {
      session.newMsg = getMsgHint(session.latestMsg);
      session.updateTime = session.latestMsg.msgTime;
    }
  });

  if (initSessionFromHash) {
    initSessionFromHash = false;

    const hash = decodeURIComponent(
      terminal ? h5Hash.get_and_clear() : location.hash,
    );

    if (hash && hash.startsWith(prefix)) {
      const sessionKey = hash.slice(prefix.length);
      let foundSession = res.itemList.find(
        (session) => session.sessionKey === sessionKey,
      );
      if (foundSession) {
        if (terminal === 1) {
          await prompt({
            title: "进入会话",
            content: `<div style="font-size: 20px;">是否进入 ${foundSession.sessionName} 会话</div>`,
            terminal,
          });
        }
        handleSelectSession(foundSession);
        return;
      }

      // 第一个分页没找到这个会话，直接查
      // 进入会话先刷新会话信息
      foundSession = await getSessionDetail({
        sessionKey,
      }).catch(() => {
        return undefined;
      });
      if (foundSession) {
        moveSessionToTop(foundSession);

        if (terminal === 1) {
          await prompt({
            title: "进入会话",
            content: `<div style="font-size: 20px;">是否进入 ${foundSession.sessionName} 会话</div>`,
            terminal,
          });
        }
        handleSelectSession(foundSession);
      } else {
        makeToast("会话不存在");
        handleSelectSession();
      }
      return;
    }
  }

  if (terminal === 0) {
    handleSelectSession(res.itemList[0]);
  }
};

async function onNewMsg(msg: Msg) {
  const sessionKey = msg.sessionKey;
  let session = sessions.itemList.find((s) => s.sessionKey === sessionKey);
  const newMsg = getMsgHint(msg);
  if (session) {
    session.newMsg = newMsg;
    session.latestMsg = msg;
    session.updateTime = msg.msgTime;
  } else {
    session = await getSessionDetail({ sessionKey });
    if (sessions.itemList.find((s) => s.sessionKey === sessionKey)) {
      // 同时收到两条相同的消息，不重复创建
      return;
    }
    session.newMsg = newMsg;
    session.latestMsg = msg;
    sessions.itemList.push(session);
  }

  if (session !== selectSession.value) {
    if (msg.msgType === 91) {
      return;
    }
    session.newMsgCount++;
    if (appStore.sessionTab === "session-list-all") {
      // 将最新消息置顶
      moveSessionToTop(session);
    }
  } else {
    session.newMsgCount = 0;
  }
}

watch(
  () => props.sse,
  (sse) => {
    if (sse) {
      sse.off("message", onNewMsg);
      sse.on("message", onNewMsg);
    }
  },
  { immediate: true },
);

onMounted(fetchSessions);
onUnmounted(() => {
  props.sse?.off("message", onNewMsg);
});

const onScroll = (e: Event) => {
  const scroller = e.target as HTMLElement;
  if (scroller.scrollTop >= scroller.scrollHeight - scroller.clientHeight) {
    fetchMore();
  }
};

let fetching = false;
const fetchMore = async () => {
  if (sessions.hasMore && !fetching) {
    fetching = true;
    const res = await getSessionList({
      sessionListType: sessionListType.value,
      tagCode: appStore.sessionTab,
      cursor: sessions.cursor,
    }).finally(() => {
      fetching = false;
    });
    Object.assign(sessions, {
      ...res,
      itemList: uniqBy(
        [...sessions.itemList, ...res.itemList],
        (session) => session.sessionKey,
      ),
    });
  }
};

const removeSession = (session: Session) => {
  // NOTICE: 这里不能用===比较，因为session.itemList里的元素都是Proxy，而传进来的session对象可能是原始对象
  const idx = sessions.itemList.findIndex(
    (s) => s.sessionKey === session.sessionKey,
  );
  if (idx !== -1) {
    sessions.itemList.splice(idx, 1);
  }
};

const moveSessionToTop = (session: Session) => {
  removeSession(session);

  // 置顶的会话来了新消息也得往最上面顶
  if (session.isPinned) {
    sessions.itemList.unshift(session);
    return;
  }

  // 否则紧接着放在置顶列表后面
  const firstUnPinnedIdx = sessions.itemList.findIndex((s) => !s.isPinned);
  sessions.itemList.splice(firstUnPinnedIdx, 0, session);
};

const handleMarkProcessedSession = async (session: Session) => {
  await markProcessed({
    sessionListType: sessionListType.value,
    sessionKey: session.sessionKey,
  });
  const removedCurrent = session === selectSession.value;
  removeSession(session);
  if (!terminal && removedCurrent) {
    handleSelectSession(sessions.itemList[0]);
  }
};

const handleMarkRepliedSession = async (session: Session) => {
  await markReplied({
    sessionKey: session.sessionKey,
  });
  const removedCurrent = session === selectSession.value;
  removeSession(session);
  if (!terminal && removedCurrent) {
    handleSelectSession(sessions.itemList[0]);
  }
};

useEventEmitter(
  emitter,
  "goSession",
  async function goSession(session: Session | string) {
    let foundSession: Session | undefined;
    if (typeof session === "string") {
      foundSession = sessions.itemList.find((s) => s.sessionKey === session);
      if (!foundSession) {
        foundSession = await getSessionDetail({
          sessionKey: session,
        });
        moveSessionToTop(foundSession);
      }
    } else {
      foundSession = sessions.itemList.find(
        (s) => s.sessionKey === session.sessionKey,
      );
      if (!foundSession) {
        foundSession = session;
        moveSessionToTop(foundSession);
      }
    }
    handleSelectSession(foundSession);
  },
);

async function handlePinnedSession(session: Session) {
  await updatePinnedStatus({
    sessionKey: session.sessionKey,
    status: !session.isPinned,
    tagCode: appStore.sessionTab,
  });
  session.isPinned = !session.isPinned;
  moveSessionToTop(session);
}

async function handleNoDisturbSession(session: Session) {
  const noDisturb = isNoDisturb(session);
  await updateNoDisturbStatus({
    sessionKey: session.sessionKey,
    status: !noDisturb,
    tagCode: appStore.sessionTab,
  });
  if (noDisturb) {
    // 取消免打扰
    remove(noDisturbList.value, session.sessionKey);
  } else {
    // 免打扰
    noDisturbList.value.push(session.sessionKey);
  }
}
</script>
<style lang="less" scoped>
:not(.sessionActive).sessionHover:hover {
  background-color: rgba(4, 165, 255, 0.2);
}

.sessionActive {
  background-color: rgba(4, 165, 255, 1);

  * {
    color: white !important;
  }
}

.sessionPinned {
  position: relative;

  &:before {
    position: absolute;
    z-index: -1;
    content: "";
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    background-color: #e6f2fb;
  }

  &.sessionPinnedFirst:before {
    border-top-left-radius: 12px;
    border-top-right-radius: 12px;
  }

  &.sessionPinnedLast:before {
    border-bottom-left-radius: 12px;
    border-bottom-right-radius: 12px;
  }
}

.normalTab {
  font-size: 12px;
  height: 28px;
  padding: 0 17px;
  border-radius: 9999px;
  color: #6e6e6e;
  background-color: white;
  display: flex;
  align-items: center;
}

.activeTab {
  font-size: 12px;
  height: 28px;
  padding: 0 17px;
  border-radius: 9999px;
  color: #04a5ff;
  background-color: rgba(4, 165, 255, 0.08);
  border: 1px solid rgba(4, 165, 255, 0.6);
  font-weight: bold;
  display: flex;
  align-items: center;
}

.virtualList {
  width: 100%;
}

.item {
  position: relative;
  display: flex;
  padding: 10px 0 0 10px;

  .avatar {
    width: 1rem;
    height: 1rem;
    border-radius: 50%;
  }

  .content {
    margin-left: 0.3rem;
    font-size: 0.3rem;
    width: 100%;
    border-bottom: 1px solid #ccc;

    .sessionName {
      width: 3rem;
      overflow: hidden;
      text-overflow: ellipsis; //文本溢出显示省略号
      white-space: nowrap; //文本不会换行
    }

    .describe {
      width: 3rem;
      font-size: 0.3rem;
      color: #999;
      overflow: hidden;
      text-overflow: ellipsis; //文本溢出显示省略号
      white-space: nowrap; //文本不会换行
    }
  }

  .updateTime {
    position: absolute;
    font-size: 0.22rem;
    right: 0.2rem;
  }

  .newMsgCount {
    text-align: center;
    width: 0.4rem;
    height: 0.4rem;
    line-height: 0.4rem;
    position: absolute;
    background-color: red;
    font-size: 0.3rem;
    border-radius: 50%;
    color: #fff;
    left: 0.9rem;
  }
}
</style>
